<div class="top-section">
    <h4 class="heading mb-0">{{ cc.texts()['screen_settings_title'] }}</h4>
</div>

<div class="body-section bg-3 position-relative">
    <div class="app-lock ri-bb-3">
        <h6 class="text-16-400 color-35 mb-0 ri-px-4 ri-py-2 ri-bb-1">{{ cc.texts()['screen_settings_sectionLockTitle'] }}</h6>
        <div class="flex items-center justify-between ri-px-4 ri-py-3">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_common_mevolvePin'] }}</p>
            <app-input-toggle [value]="metaSignals()['isPasscodeEnabled']() && metaSignals()['passcode']()" (toggle)="togglePasscode($event)" [readonly]="true" [disableValueChange]="true" [pointer]="true"></app-input-toggle>
        </div>
        <div class="flex items-center justify-between ri-px-4 ri-py-3" *ngIf="metaSignals()['isPasscodeEnabled']() && metaSignals()['passcode']()">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_settings_lockReset'] }}</p>
            <p class="text-16-400 color-35 mb-0" role="button" (click)="openPinDialog('edit')">{{ cc.texts()['screen_settings_lockPinReset'] }}</p>
        </div>
        <div class="flex items-center justify-between ri-px-4 ri-py-3" *ngIf="metaSignals()['isPasscodeEnabled']() && metaSignals()['passcode']()">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_settings_lockDeviceSecurity'] }}</p>
            <app-input-toggle [signal]="metaSignals()['isBiometricEnabled']" (toggle)="ss.updateUserSettings()"></app-input-toggle>
        </div>
    </div>
    <div class="app-experience ri-bb-3">
        <h6 class="text-16-400 color-35 mb-0 ri-px-4 ri-py-2 ri-bb-1">{{ cc.texts()['screen_settings_sectionExperienceTitle'] }}</h6>
        <div class="flex items-center justify-between ri-px-4 ri-py-3">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_common_theme'] }}</p>
            <app-input-dropdown [signal]="viewSignals()['appTheme']" name="appTheme" [config]="appThemeConfig()" [selectedMark]="'TICK'"></app-input-dropdown>
        </div>
        <div class="flex items-center justify-between ri-px-4 ri-py-3">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_settings_experienceColour'] }}</p>
            <span class="selected-theme" [style.border-color]="cc.theme().color1" role="button" (click)="openThemeDialog()">
                <span class="selected-theme-color" [style.background-color]="cc.theme().color1"></span>
            </span>
        </div>
        <div class="flex items-center justify-between ri-px-4 ri-py-3">
            <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_settings_experienceVibration'] }}</p>
            <app-input-toggle [signal]="viewSignals()['isVibrationEnabled']" (toggle)="vs.updateView()"></app-input-toggle>
        </div>
        <div class="flex items-center justify-between ri-px-4 ri-py-3">
            <div>
                <p class="text-16-400 color-8 mb-0">{{ cc.texts()['screen_settings_speechToText'] }}</p>
                <span class="text-12-400 color-7">{{ cc.texts()['screen_settings_speechToTextContent'] }}</span>
            </div>
            <app-input-toggle [signal]="viewSignals()['isSpeechToTextEnabled']" (toggle)="vs.updateView()"></app-input-toggle>
        </div>
    </div>
    <div class="app-version-block">
        <h6 class="text-16-400 color-35 mb-0 ri-px-4 ri-py-2 ri-bb-1">{{ cc.texts()['screen_settings_version'] }}</h6>
        <div class="flex items-center ri-px-4 ri-py-3">
            <app-svg name="info" [color]="cc.theme().color35" class="ri-me-2" style="width: 22px;height: 22px;"></app-svg>
            <p class="text-16-400 color-8 mb-0">v{{ cc.appVersion() }}.{{ cc.appSubVersion() }}.{{ cc.dbVersion() }}+{{cc.buildVersion()}}<span *ngIf="cc.env() !== 'prod'">({{ cc.env() }})</span></p>
        </div>
        <div class="flex items-center ri-px-4 ri-py-3">
            <app-svg name="calendar" [color]="cc.theme().color35" class="ri-me-3"></app-svg>
            <p class="text-16-400 color-8 mb-0">{{ getUtcDateString() }}</p>
        </div>
        <div class="flex items-center ri-px-4 ri-py-3">
            <app-svg name="clock" [color]="cc.theme().color35" class="ri-me-3"></app-svg>
            <p class="text-16-400 color-8 mb-0">{{ cc.buildUpdatedDate() | parseUpdateResponse }}</p>
        </div>
    </div>
</div>