import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { getNewId } from '../_utils/utils';
import { StorageService } from './storage.servive';
import { DownloadActionValue, Screen } from '@app/_types/analytics-events.types';

@Injectable({
    providedIn: 'root',
})

export class AnalyticsService {

    commonProperties = {
        device_id: 'NA',
        device_type: 'NA',
        device_os: 'NA',
        browser: 'NA',
        country: 'NA',
        language: 'NA',
        session_id: 'NA',
        muid: 'NA',
        env: environment.env,
    }

    initialParams = {
        initial_utm_source: 'NA',
        initial_utm_medium: 'direct',
        initial_utm_campaign: 'NA',
        initial_utm_content: 'NA',
        initial_d_id: 'NA',
        initial_r_id: 'NA',
    };

    latestParams = {
        latest_utm_source: 'NA',
        latest_utm_medium: 'direct',
        latest_utm_campaign: 'NA',
        latest_utm_content: 'NA',
        latest_d_id: 'NA',
        latest_r_id: 'NA',
    };

    constructor(private http: HttpClient, private storageService: StorageService) { };

    mePageView(value: Screen, itemId: string) {
        this.getLatestParams();
        const eventData = {
            id: getNewId(),
            event: 'pp_public_screen_viewed',
            action_value: value,
            item_id: itemId,
            timestamp: new Date(),
            ...this.initialParams,
            ...this.latestParams,
            ...this.commonProperties
        };
        const eventParams = {
            action_value: eventData.action_value,
            item_id: eventData.item_id
        };
        const eventAddons = {
            initial_traffic_params: {
                utm_campaign: this.initialParams.initial_utm_campaign,
                utm_source: this.initialParams.initial_utm_source,
                utm_content: this.initialParams.initial_utm_content,
                utm_medium: this.initialParams.initial_utm_medium,
                d_id: this.initialParams.initial_d_id,
                r_id: this.initialParams.initial_r_id
            },
            latest_traffic_params: {
                utm_campaign: this.latestParams.latest_utm_campaign,
                utm_source: this.latestParams.latest_utm_source,
                utm_content: this.latestParams.latest_utm_content,
                utm_medium: this.latestParams.latest_utm_medium,
                d_id: this.latestParams.latest_d_id,
                r_id: this.latestParams.latest_r_id
            }
        };
        this.sendEvent(eventData, eventParams, eventAddons);
    }

    mePublicProfileClickEvent(value: 'follow' | 'list' | 'note' | 'profile_share', itemId: string) {
        this.getLatestParams();
        const eventData = {
            id: getNewId(),
            event: 'pp_public_profile_cta_clicks',
            action_value: value,
            item_id: itemId,
            timestamp: new Date(),
            ...this.initialParams,
            ...this.latestParams,
            ...this.commonProperties
        };
        const eventParams = {
            action_value: eventData.action_value,
            item_id: eventData.item_id
        };
        const eventAddons = {
            initial_traffic_params: {
                utm_campaign: this.initialParams.initial_utm_campaign,
                utm_source: this.initialParams.initial_utm_source,
                utm_content: this.initialParams.initial_utm_content,
                utm_medium: this.initialParams.initial_utm_medium,
                d_id: this.initialParams.initial_d_id,
                r_id: this.initialParams.initial_r_id
            },
            latest_traffic_params: {
                utm_campaign: this.latestParams.latest_utm_campaign,
                utm_source: this.latestParams.latest_utm_source,
                utm_content: this.latestParams.latest_utm_content,
                utm_medium: this.latestParams.latest_utm_medium,
                d_id: this.latestParams.latest_d_id,
                r_id: this.latestParams.latest_r_id
            }
        };
        this.sendEvent(eventData, eventParams, eventAddons);
    }

    meSaveEvent(value: 'note' | 'list', itemId: string) {
        this.getLatestParams();
        const eventData = {
            id: getNewId(),
            event: 'pp_public_screen_save',
            action_value: value,
            item_id: itemId,
            timestamp: new Date(),
            ...this.initialParams,
            ...this.latestParams,
            ...this.commonProperties
        };
        const eventParams = {
            action_value: eventData.action_value,
            item_id: eventData.item_id
        };
        const eventAddons = {
            initial_traffic_params: {
                utm_campaign: this.initialParams.initial_utm_campaign,
                utm_source: this.initialParams.initial_utm_source,
                utm_content: this.initialParams.initial_utm_content,
                utm_medium: this.initialParams.initial_utm_medium,
                d_id: this.initialParams.initial_d_id,
                r_id: this.initialParams.initial_r_id
            },
            latest_traffic_params: {
                utm_campaign: this.latestParams.latest_utm_campaign,
                utm_source: this.latestParams.latest_utm_source,
                utm_content: this.latestParams.latest_utm_content,
                utm_medium: this.latestParams.latest_utm_medium,
                d_id: this.latestParams.latest_d_id,
                r_id: this.latestParams.latest_r_id
            }
        };
        this.sendEvent(eventData, eventParams, eventAddons);
    }

    meDownloadEvent(value: DownloadActionValue, location: 'follow' | 'save_list' | 'save_note' | 'other') {
        this.getLatestParams();
        const eventData = {
            id: getNewId(),
            event: 'pp_download_click',
            action_value: value,
            timestamp: new Date(),
            // utm_source: 'public_webpage',
            // utm_content: location,
            public: true,
            // utm_campaign: location === 'save_list' ? 'public_list_share' : location === 'save_note' ? 'public_note_share' : 'public_profile',
            ...this.initialParams,
            ...this.latestParams,
            ...this.commonProperties,

        };
        const eventParams = {
            action_value: eventData.action_value,
            public: eventData.public,
        };
        const eventAddons = {
            initial_traffic_params: {
                utm_campaign: this.initialParams.initial_utm_campaign,
                utm_source: this.initialParams.initial_utm_source,
                utm_content: this.initialParams.initial_utm_content,
                utm_medium: this.initialParams.initial_utm_medium,
                d_id: this.initialParams.initial_d_id,
                r_id: this.initialParams.initial_r_id
            },
            latest_traffic_params: {
                utm_campaign: this.latestParams.latest_utm_campaign,
                utm_source: this.latestParams.latest_utm_source,
                utm_content: this.latestParams.latest_utm_content,
                utm_medium: this.latestParams.latest_utm_medium,
                d_id: this.latestParams.latest_d_id,
                r_id: this.latestParams.latest_r_id
            }
        };
        this.sendEvent(eventData, eventParams, eventAddons);
    }

    meClickEvent(value: 'add_subscriber') {
        this.getLatestParams();
        const eventData = {
            id: getNewId(),
            event: 'pp_internal_click',
            action_value: value,
            timestamp: new Date(),
            public: true,
            ...this.initialParams,
            ...this.latestParams,
            ...this.commonProperties,

        };
        const eventParams = {
            action_value: eventData.action_value,
            public: eventData.public,
        };
        const eventAddons = {
            initial_traffic_params: {
                utm_campaign: this.initialParams.initial_utm_campaign,
                utm_source: this.initialParams.initial_utm_source,
                utm_content: this.initialParams.initial_utm_content,
                utm_medium: this.initialParams.initial_utm_medium,
                d_id: this.initialParams.initial_d_id,
                r_id: this.initialParams.initial_r_id
            },
            latest_traffic_params: {
                utm_campaign: this.latestParams.latest_utm_campaign,
                utm_source: this.latestParams.latest_utm_source,
                utm_content: this.latestParams.latest_utm_content,
                utm_medium: this.latestParams.latest_utm_medium,
                d_id: this.latestParams.latest_d_id,
                r_id: this.latestParams.latest_r_id
            }
        };
        this.sendEvent(eventData, eventParams, eventAddons);
    }

    sendEvent(payload: any, eventParams: any, payloadAddons?: any) {
        const eventPayload = {
            id: payload.id,
            event_name: payload.event,
            event_type: 'user_action',
            event_timestamp: payload.timestamp,
            platform: `web_${this.commonProperties.device_type}`,
            muid: this.commonProperties.muid,
            user_params: {
                language: this.commonProperties.language,
                device_id: this.commonProperties.device_id,
                device_type: this.commonProperties.device_type,
                device_os: this.commonProperties.device_os,
                browser: this.commonProperties.browser
            },
            platform_params: {
                session_id: this.commonProperties.session_id,
                env: this.commonProperties.env
            },
            event_params: {
                ...eventParams
            },
            ...payloadAddons
        };

        this.sendEventApi(eventPayload).subscribe({
            next: (res) => { },
            error: (err) => console.error('Error sending event', err)
        });
    }

    sendEventApi(payload: any) {
        const headers = new HttpHeaders({
            'Content-Type': 'application/json',
            'x-api-key': environment.analyticsApiKey,
        });

        return this.http.post(`${environment.analyticsApiBase}/${environment.analyticsProjectId}`, payload, { headers });
    }

    getLatestParams() {
        this.latestParams.latest_utm_source = this.storageService.getLastUtmSource() || 'NA';
        this.latestParams.latest_utm_medium = this.storageService.getLastUtmMedium() || 'direct';
        this.latestParams.latest_utm_campaign = this.storageService.getLastUtmCampaign() || 'NA';
        this.latestParams.latest_utm_content = this.storageService.getLastUtmContent() || 'NA';
        this.latestParams.latest_d_id = this.storageService.getLastDesignerId() || 'NA';
        this.latestParams.latest_r_id = this.storageService.getLastReferrerId() || 'NA';
    }
}