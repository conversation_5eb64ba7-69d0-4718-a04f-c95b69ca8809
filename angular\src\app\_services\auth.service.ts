import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Auth, GoogleAuthProvider, OAuthProvider, signInWithPopup, signInWithRedirect, getRedirectResult } from '@angular/fire/auth';
import { StorageService } from './storage.servive';
import { IndexDbService } from './index-db.service';
import { CryptographyService } from "./cryptography.service";
import { getNewId } from '@app/_utils/utils';
import { User, UserData } from '@app/_interfaces/user.interface';
import { FirebaseFunctionService } from './firebase-function.service';
import { CacheService } from './cache.service';
import { MatDialog } from '@angular/material/dialog';
import { MigrationComponent } from '@app/components/addons/migration/migration.component';
import { CustomEncryptionKeyComponent } from '@app/components/auth/custom-encryption-key/custom-encryption-key.component';
import { Subject, takeUntil } from 'rxjs';
import { environment } from '@environments/environment';

@Injectable({
  providedIn: 'root',
})

export class AuthService {

  private readonly dbVersion: number = environment.dbVersion;
  googleLoginLoading: boolean = false;
  appleLoginLoading: boolean = false;
  migrationLoading: boolean = false;
  setupLoading: boolean = false;
  unSubscribe = new Subject<void>();

  constructor(
    private cryptoService: CryptographyService,
    private storageService: StorageService,
    private cc: CacheService,
    private fbfs: FirebaseFunctionService,
    private router: Router,
    private auth: Auth,
    private idb: IndexDbService,
    private dialog: MatDialog
  ) {

  }

  public async init(loggedUserData?: UserData) {

    this.setupLoading = true;

    let user = this.storageService.getUser();
    if (!user && !loggedUserData) {
      this.logoutUser();
      this.setupLoading = false;
      return;
    };

    if (loggedUserData) {
      try {
        const userSecret = await this.getEncryptionKey(loggedUserData.email);
        this.storageService.saveSecretToLocalStorage(this.cryptoService.userSecretKey, loggedUserData.uid, userSecret);
        const userDetails = await this.getUser(loggedUserData, userSecret);

        if (userDetails.user.docVer < this.dbVersion) {
          this.setupLoading = false;
          this.openMigrationDialog(loggedUserData);
          return;
        }

        console.log("userDetails", userDetails);
        user = this.cryptoService.decrypt(userDetails.user, false) as User;
        console.log("decrypted user::", user);
        this.storageService.setUser(user);
      } catch (error) {
        console.log("error", error);
        this.setupLoading = false;
        this.logoutUser();
        return;
      }
    };

    console.log("first check completed-->", user);

    if (!user) {
      this.setupLoading = false;
      this.logoutUser();
      return;
    }

    const userData = {
      uid: user.uid,
      email: user.userInfo.email,
      name: user.userInfo.name,
      pseudoName: user.userInfo.pseudoName,
      hashedEmail: this.storageService.getHashedEmail(),
      isNewUser: false
    }
    this.cc.userData = user;
    this.cc.user = userData;
    const { uid, email } = userData;

    this.cryptoService.aesSecretKey = this.storageService.getSecretFromLocal(this.cryptoService.userSecretKey, uid);
    this.cryptoService.eccPublicKeySPKI = this.storageService.getSecretFromLocal(this.cryptoService.eccPublicKey, uid);
    this.cryptoService.eccPrivateKeyPKCS8 = this.storageService.getSecretFromLocal(this.cryptoService.eccPrivateKey, uid);
    this.cryptoService.eccEncPrivateKeyPKCS8 = this.storageService.getSecretFromLocal(this.cryptoService.eccEncPrivateKey, uid);

    console.log("second check completed-->", user);

    if (!this.cryptoService.aesSecretKey) {
      try {
        const userSecret = await this.getEncryptionKey(email);
        this.storageService.saveSecretToLocalStorage(this.cryptoService.userSecretKey, user.uid, userSecret);
        const userDetails = await this.getUser(userData, userSecret);

        this.cryptoService.aesSecretKey = this.storageService.getSecretFromLocal(this.cryptoService.userSecretKey, uid);
        this.cryptoService.eccPublicKeySPKI = this.storageService.getSecretFromLocal(this.cryptoService.eccPublicKey, uid);
        this.cryptoService.eccPrivateKeyPKCS8 = this.storageService.getSecretFromLocal(this.cryptoService.eccPrivateKey, uid);
        this.cryptoService.eccEncPrivateKeyPKCS8 = this.storageService.getSecretFromLocal(this.cryptoService.eccEncPrivateKey, uid);

        console.log("userDetails after getting secret", userDetails);
        user = this.cryptoService.decrypt(userDetails.user, false) as User;
        this.storageService.setUser(user);

      } catch (error) {
        this.setupLoading = false;
        this.logoutUser();
        return;
      }
    }

    console.log("third check completed-->", user);

    if (!this.storageService.isResynced()) {
      await this.idb.resync();
      this.storageService.setResynced(true);
    }

    this.idb.onInit();

    console.log("fourth check completed-->", user);

    this.setupLoading = false;
    if (loggedUserData) {
      this.router.navigate(['/today']);
    }

    return user;
  }

  // Get user
  private async getUser(user: UserData, userSecret: string): Promise<{ user: User; viewSettings: any; userKeys: any }> {
    try {
      const { name, email } = user;

      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const sessionId = this.storageService.getSessionId() || getNewId();
      this.storageService.setSessionId(sessionId);

      const payload = {
        sessionId: sessionId,
        displayName: name,
        email: email,
        timeZone: timezone,
      }

      const userDetails = await this.fbfs.callableFunction('user-get', payload, true) as { user: string, viewSettings: string, userKey: string };

      if (!userDetails || !userDetails.user || !userDetails.userKey) {
        throw new Error('User not found.');
      }

      const userKey = JSON.parse(userDetails.userKey);
      this.storageService.saveSecretToLocalStorage(this.cryptoService.eccPublicKey, user.uid, userKey.publicKey);
      this.storageService.saveSecretToLocalStorage(this.cryptoService.eccEncPrivateKey, user.uid, userKey.encPrivateKey);
      this.storageService.setHashedEmail(userKey.emailHash);

      if (userSecret && userKey.privateKeyPasswordType == 'KMS') {
        console.log("kms login--->>", userKey);
        const privateKey = this.cryptoService.decryptValue(userKey.encPrivateKey, userSecret);

        if (privateKey) {
          this.storageService.saveSecretToLocalStorage(this.cryptoService.eccPrivateKey, user.uid, privateKey);
          this.cryptoService.eccPrivateKeyPKCS8 = privateKey;
        }
      } else if (userKey.privateKeyPasswordType == 'CUSTOM') {
        this.setupLoading = false;
        const user = JSON.parse(userDetails.user) as User;
        const privateKey = await this.openCustomEncDialog(user, userKey.encPrivateKey);
        if (privateKey) {
          this.setupLoading = true;
          this.storageService.saveSecretToLocalStorage(this.cryptoService.eccPrivateKey, user.uid, privateKey);
          this.cryptoService.eccPrivateKeyPKCS8 = privateKey;
        } else {
          throw new Error('Custom encryption key is required.');
        }

      } else {
        throw new Error('Invalid private key password type.');
      }

      return {
        user: JSON.parse(userDetails.user),
        viewSettings: JSON.parse(userDetails.viewSettings),
        userKeys: userKey
      };
    } catch (error: any) {
      this.setupLoading = false;
      throw error;
    }
  }

  // Get the user encryption key
  private async getEncryptionKey(email: string): Promise<string> {
    try {
      const sessionId = this.storageService.getSessionId() || getNewId();
      this.storageService.setSessionId(sessionId);

      const payload = {
        sessionId,
        email,
      };

      const response = await this.fbfs.callableFunction('me-encryption-getKeyForUser', payload) as any;

      if (!response || !response.key) {
        throw new Error('Encryption key not found in response.');
      }
      return response.key;
    } catch (error: any) {
      this.setupLoading = false;
      throw error;
    }
  }

  openMigrationDialog(userData: UserData) {
    const migrationDialog = this.dialog.open(MigrationComponent, {
      width: '100%',
      maxWidth: '500px',
      disableClose: true,
      data: {
        userData: userData
      }
    });
    migrationDialog.afterClosed().subscribe((result) => {
      if (result) {
        this.init(userData);
      } else {
        this.logoutUser();
      }
    });
  }

  async openCustomEncDialog(userData: User, encPrivateKey: string): Promise<string | null> {
    const encKeyDialog = this.dialog.open(CustomEncryptionKeyComponent, {
      width: '100%',
      maxWidth: '500px',
      disableClose: true,
      data: {
        userData: userData,
        encPrivateKey: encPrivateKey
      }
    });
    return Promise.resolve(encKeyDialog.afterClosed().toPromise());
  }

  async loginWithGoogle() {
    this.googleLoginLoading = true;
    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
      prompt: 'select_account'
    });
    try {
      const result = await signInWithPopup(this.auth, provider);
      const user: UserData = {
        uid: result.user.uid,
        email: result.user.email || '',
        name: result.user.displayName || '',
        pseudoName: '',
        isNewUser: false,
        hashedEmail: ''
      }

      this.googleLoginLoading = false;
      this.init(user);
    } catch (error) {
      console.log("error", error);
      this.googleLoginLoading = false;
      // Handle errors here
    }
  }

  async loginWithApple(): Promise<void> {
    try {
      this.appleLoginLoading = true;
      sessionStorage.setItem('appleRedirectInProgress', 'true');
      const appleProvider = new OAuthProvider('apple.com');
      appleProvider.addScope('email');
      appleProvider.addScope('name');
      appleProvider.setCustomParameters({
        locale: this.cc.language()
      });

      // Use redirect instead of popup
      await signInWithRedirect(this.auth, appleProvider);
    } catch (error) {
      sessionStorage.removeItem('appleRedirectInProgress');
      // alert('Apple Sign-In Error: ' + error);
      console.error('Apple Sign-In Error:', error);
      this.appleLoginLoading = false;
      throw new Error('LogInWithAppleFailure');
    }
  }

  // In another part of your app (like on ngOnInit)
  async handleAppleRedirectResult(): Promise<void> {
    this.appleLoginLoading = sessionStorage.getItem('appleRedirectInProgress') === 'true';
    console.log("isAppleRedirect", this.appleLoginLoading);
    try {
      const result = await getRedirectResult(this.auth);
      if (result) {
        const user: UserData = {
          uid: result.user.uid,
          email: result.user.email || '',
          name: result.user.displayName || '',
          pseudoName: '',
          isNewUser: false,
          hashedEmail: ''
        };
        this.init(user);
      } else {
        console.log('No redirect result found.');
      }
    } catch (error) {
      console.error('Apple Redirect Handling Error:', error);
    } finally {
      sessionStorage.removeItem('appleRedirectInProgress');
      this.appleLoginLoading = false;
    }
  }

  // async loginWithApple(): Promise<void> {
  //   try {
  //     // Initialize Apple OAuthProvider
  //     const appleProvider = new OAuthProvider('apple.com');

  //     // Add custom scopes if needed
  //     appleProvider.addScope('email');
  //     appleProvider.addScope('name');

  //     signInWithPopup(this.auth, appleProvider)
  //       .then(async (result) => {
  //         // The signed-in user info.

  //         // Apple credential
  //         // const credential = OAuthProvider.credentialFromResult(result);
  //         // const accessToken = credential?.accessToken;
  //         // const idToken = credential?.idToken;

  //         const user: UserData = {
  //           uid: result.user.uid,
  //           email: result.user.email || '',
  //           name: result.user.displayName || ''
  //         }

  //         this.init(user);

  //         // IdP data available using getAdditionalUserInfo(result)
  //         // ...
  //       })
  //       .catch((error) => {

  //         // ...
  //       });
  //   } catch (error) {
  //     console.error('Apple Sign-In Error:', error);
  //     throw new Error('LogInWithAppleFailure');
  //   }
  // }

  get isLoggedIn(): boolean {
    const localUser = this.storageService.getUser();
    if (localUser && localUser?.uid) {
      return true;
    } else {
      return false;
    }
  }

  logoutUser() {
    this.storageService.clearLocalStore();
    this.auth.signOut();
    this.idb.deleteIndexDb();
    this.idb.stopSubscription();
    this.router.navigate(['/login']);
  }
}
