import { CommonModule } from '@angular/common';
import { Component, computed, inject, Input, OnChanges, signal, Signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { EntityGroup, EntitySetup } from '@app/_interfaces/feature.interface';
import { CacheService } from '@app/_services/cache.service';
import { CalendarIntegrationStore } from '@app/_stores';
import { CalendarEventBlockComponent } from '../calendar-event-block/calendar-event-block.component';
import { FilterHashtagComponent } from '@app/components/shared/filters/filter-hashtag/filter-hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FilterDateRangeComponent } from '@app/components/shared/filters/filter-date-range/filter-date-range.component';
import { FilterSearchComponent } from '@app/components/shared/filters/filter-search/filter-search.component';
import { DragScrollDirective } from '@app/_directives/drag-scroll.directive';
import { VirtualScrollerComponent, VirtualScrollerModule } from '@iharbeck/ngx-virtual-scroller';

@Component({
  selector: 'app-calendar-tab',
  standalone: true,
  imports: [
    CommonModule,
    CalendarEventBlockComponent,
    FilterHashtagComponent,
    SvgComponent,
    FormsModule,
    ReactiveFormsModule,
    FilterDateRangeComponent,
    FilterSearchComponent,
    DragScrollDirective,
    VirtualScrollerModule
  ],
  templateUrl: './calendar-tab.component.html',
  styleUrl: './calendar-tab.component.scss'
})

export class CalendarTabComponent implements OnChanges {

  readonly calendarStore = inject(CalendarIntegrationStore);
  @Input() mode: Signal<'PAST' | 'FUTURE'> = computed(() => 'PAST');
  @Input() show: string[] = [];
  @Input() groupedViewType: string[] = [];
  @Input() filter: boolean = false;
  @Input() activeTab: string = 'habit';
  @Input() groupType: string = 'date';
  @Input() descriptionType: string = 'none';

  filterSearch = signal<string>('');
  startDate = signal<Date | null>(null);
  endDate = signal<Date | null>(null);

  scrollOffset: number = 0;

  calendarEvents: Signal<EntityGroup[]> = computed(() => {
    const firstDateString = this.calendarStore.firstDateString();
    const firstDate = new Date(firstDateString || '');
    const group: EntityGroup[] = [];

    let dateStrings: string[] = [];
    switch (this.mode()) {
      case 'PAST':
        dateStrings = this.cc.getPastDates(this.startDate() || firstDate, this.endDate() ? this.endDate() : null);
        break;
      case 'FUTURE':
        dateStrings = this.endDate() === null && this.startDate() === null ? this.cc.futureDates() : this.cc.getFutureDates(this.endDate(), this.startDate());
        break;
    }

    dateStrings.forEach(dateString => {
      const entities = this.calendarStore.getComputedEntities(dateString, 'date');
      let allEntities = [...entities.withoutTime, ...entities.withTime];

      allEntities = this.applyFilter(allEntities, dateString);
      group.push({
        id: dateString,
        name: dateString,
        data: allEntities
      });
    });
    return group;
  });

  constructor(public cc: CacheService) {
  }

  ngOnChanges(changes: SimpleChanges): void {

  }

  ngOnInit() {

  }

  applyFilter(entities: EntitySetup[], dateString: string): EntitySetup[] {
    // Filters
    const search = this.filterSearch().toLowerCase();
    return entities.filter(entity => {
      // const calendarEvent = this.calendarStore.idToEvent()[entity.id];
      // const event = calendarEvent ? { ...calendarEvent } : null;
      const matchesSearch = !search || entity.title.toLowerCase().includes(search);

      return matchesSearch;
    });
  }

  getIsOpen() {
    return this.groupedViewType.includes('collapsedView') ? 'false' : 'true';
  }

  isFiltered() {
    return this.filterSearch() || this.startDate() || this.endDate();
  }

  clearFilter() {
    this.filterSearch.set('');
    this.startDate.set(null);
    this.endDate.set(null);
  }

  trackByForEntity(index: number, entity: EntitySetup) {
    return entity.id;
  }

  trackByForGroup(index: number, group: EntityGroup) {
    return group.id;
  }

  toggleChildGroup(element: HTMLElement, groupId: string) {
    const isOpened = element.getAttribute('isOpen');
    const groupedElement = document.getElementById(groupId);
    if (!groupedElement) {
      return
    }
    if (isOpened === 'false') {
      groupedElement.setAttribute('isGroupOpened', 'true');
      element.setAttribute('isOpen', 'true');
    } else {
      groupedElement.setAttribute('isGroupOpened', 'false');
      element.setAttribute('isOpen', 'false');
    }
  }
}
