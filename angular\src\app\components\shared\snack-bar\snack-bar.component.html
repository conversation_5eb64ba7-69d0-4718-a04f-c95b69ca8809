<div class="snackbar-content">
    <p class="text-16-400 color-12 mb-0 ri-pb-6">{{ ss.texts()[messageText] }}</p>
    <div class="flex justify-end">
        <button class="btn text-btn color-12 ri-me-6" (click)="secondaryAction()" *ngIf="secondaryActionText">{{ ss.texts()[secondaryActionText] }}</button>
        <button class="btn text-btn color-12" (click)="primaryAction()">{{ ss.texts()[primaryActionText] }}</button>
    </div>
</div>