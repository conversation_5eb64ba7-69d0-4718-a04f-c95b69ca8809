import { Component, computed, inject, Input, Signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { EntitySetup } from '@app/_interfaces/feature.interface';
import { Habit, HabitSetup } from '@app/_interfaces/habit.interface';
import { CacheService } from '@app/_services/cache.service';
import { HabitStore } from '@app/_stores';
import { getDateString } from '@app/_utils/utils';
import { HabitFormComponent } from '../habit-form/habit-form.component';
import { CommonModule } from '@angular/common';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { InputCheckmarkAdvancedComponent } from '@app/components/shared/inputs/input-checkmark-advanced/input-checkmark-advanced.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { ParseRulePipe } from '@app/_pipes/parse-rule.pipe';
import { ParseTextPipe } from '@app/_pipes/parse-text.pipe';
import { DependencyService } from '@app/_services/dependency.service';
import { UserBadgeComponent } from '../../profiles/user-badge/user-badge.component';

@Component({
  selector: 'app-habit-block',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseTimePipe,
    ParseMinutesPipe,
    InputCheckmarkAdvancedComponent,
    ParseRulePipe,
    ParseTextPipe,
    UserBadgeComponent
  ],
  templateUrl: './habit-block.component.html',
  styleUrl: './habit-block.component.scss'
})

export class HabitBlockComponent {

  readonly habitStore = inject(HabitStore);
  @Input() entity!: EntitySetup;
  @Input() show: string[] = [];
  @Input() blockClass: string = '';
  @Input() dateString: string = getDateString();
  @Input() descriptionType: string = 'none';
  @Input() isLabel: boolean = true;

  setup: Signal<HabitSetup> = computed(() => {
    return this.habitStore.idToSetup()[this.entity.id];
  });

  habit: Signal<Habit | null> = computed(() => {
    return this.habitStore.habitMap().setups[this.entity.id]?.[this.dateString] || null;
  });

  hashtags: Signal<string[]> = computed(() => {
    const setupTags = this.setup().tags;
    const journalTags = this.habit() ? this.habit()!.tags : [];
    const tags = [...setupTags, ...journalTags];
    const uniqueTags = [...new Set(tags)];
    return uniqueTags;
  });

  constructor(public cc: CacheService, public dialog: MatDialog, public ds: DependencyService) {
    
  }

  openHabit(setupId: string) {
    const confirmDialog = this.dialog.open(HabitFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      minHeight: '90vh',
      disableClose: true,
      autoFocus: false,
      data: {
        mode: this.habit() ? 'edit' : 'new',
        value: this.habit(),
        setupId: setupId,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }
}
