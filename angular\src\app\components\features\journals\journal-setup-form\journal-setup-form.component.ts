import { CommonModule } from '@angular/common';
import { Component, computed, inject, Inject, signal, Signal, ViewChild, WritableSignal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { JournalSetup } from '@app/_interfaces/journal.interface';
import { CacheService } from '@app/_services/cache.service';
import { JournalStore } from '@app/_stores';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { AlertService } from '@app/_services/alert.service';
import { UtilsService } from '@app/_services/utils.service';
import { MatMenuModule } from '@angular/material/menu';
import { CollaboratorComponent } from '@app/components/addons/collaborators/collaborator/collaborator.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { InputDatePeriodComponent } from '@app/components/shared/inputs/input-date-period/input-date-period.component';
import { InputTimeComponent } from '@app/components/shared/inputs/input-time/input-time.component';
import { getCustomDate } from '@app/_utils/utils';
import { InputReminderComponent } from '@app/components/shared/inputs/input-reminder/input-reminder.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { CollaboraterRole, CollaboraterStatus } from '@app/_types/generic.type';
import { DependencyService } from '@app/_services/dependency.service';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-journal-setup-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    InputTextComponent,
    InputHashtagComponent,
    InputDatePeriodComponent,
    SvgComponent,
    InputTimeComponent,
    InputReminderComponent
  ],
  templateUrl: './journal-setup-form.component.html',
  styleUrl: './journal-setup-form.component.scss'
})

export class JournalSetupFormComponent {

  @ViewChild('jSetupForm') jSetupForm!: NgForm;
  unSubscribe = new Subject<void>();
  journalSetupForm: FormGroup;
  journalSetupInitial: JournalSetup;
  readonly journalStore = inject(JournalStore);
  submitted: boolean = false;
  mode: WritableSignal<'new' | 'edit'> = signal('new');
  setup: Signal<JournalSetup> = computed(() => {
    return this.journalStore.idToSetup()[this.data.value?.id || ''] || {} as JournalSetup;
  });
  isCollab: Signal<boolean> = computed(() => {
    return (this.setup().members?.memberHashedEmails?.length || 0) > 0;
  });

  role: Signal<CollaboraterRole> = computed(() => {
    if (this.mode() === 'new') {
      return 'owner';
    } else if (this.setup().uid === this.cc.user.uid) {
      return 'owner';
    } else if (this.setup().members) {
      return this.setup().members?.membersConfig[this.cc.user.hashedEmail]?.role;
    } else {
      return 'viewer';
    }
  });

  constructor(
    public dialogRef: MatDialogRef<JournalSetupFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: JournalSetup },
    private fb: FormBuilder,
    public cc: CacheService,
    private alertService: AlertService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    public ds: DependencyService,
    public ss: SettingsService
  ) {

    this.mode.set(data.mode);
    console.log("journal setup form data--->", data.value);
    this.journalSetupInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);

    this.journalSetupForm = this.fb.group({
      id: new FormControl(this.journalSetupInitial.id, Validators.required),
      title: new FormControl(this.journalSetupInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      duration: new FormControl(this.journalSetupInitial.duration),
      isStartTimeSet: new FormControl(this.journalSetupInitial.isStartTimeSet),
      isTmzAffected: new FormControl(this.journalSetupInitial.isTmzAffected),
      reminderAt: new FormControl(this.journalSetupInitial.reminderAt),
      repeat: new FormControl(this.journalSetupInitial.repeat),
      startAt: new FormControl(this.journalSetupInitial.startAt),
      endAt: new FormControl(this.journalSetupInitial.endAt),
      tags: new FormControl(this.journalSetupInitial.tags),
      uid: new FormControl(this.journalSetupInitial.uid, Validators.required)
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(journalSetup?: JournalSetup): JournalSetup {
    return {
      id: journalSetup ? journalSetup.id : this.utilsService.getNewId(),
      title: journalSetup ? journalSetup.title : '',
      duration: journalSetup ? journalSetup.duration : 15,
      isStartTimeSet: journalSetup ? journalSetup.isStartTimeSet : false,
      isTmzAffected: journalSetup ? journalSetup.isTmzAffected : false,
      reminderAt: journalSetup ? journalSetup.reminderAt : [],
      repeat: journalSetup ? journalSetup.repeat : ["RRULE:FREQ=DAILY"],
      startAt: journalSetup ? journalSetup.startAt : getCustomDate(),
      endAt: journalSetup ? journalSetup.endAt : null,
      tags: journalSetup ? journalSetup.tags : [],
      uid: journalSetup ? journalSetup.uid : this.cc.user.uid,
    }
  }

  getFc(fcName: string): FormControl {
    return this.journalSetupForm.get(fcName) as FormControl;
  }

  hasChanges() {
    const initial = _.cloneDeep(this.journalSetupInitial);
    const current = _.cloneDeep(this.journalSetupForm.value);
    return !_.isEqual(initial, current);
  }

  addCollaborator() {
    const confirmDialog = this.dialog.open(CollaboratorComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
      data: {
        collection: 'journalSetups',
        entityData: this.data.value,
        isPublic: false,
      },
    });
    return confirmDialog.afterClosed();
  }

  async removeCollab(action: CollaboraterStatus) {
    const res = await this.alertService.confirm(this.cc.texts()[action === 'left' ? 'overlay_leaveJournal_title' : 'overlay_blockJournal_title'], this.cc.texts()[action === 'left' ? 'overlay_leaveJournal_content' : 'overlay_blockJournal_content'], this.cc.texts()[action === 'left' ? 'screen_common_sharingLeave' : 'screen_common_sharingBlock'], this.cc.texts()['screen_common_buttonCancel'], 'color-11', 'color-7');
    if (!res) return;
    if (res.confirm === true) {
      await this.ss.leaveCollaboratedEntity(action, this.setup().id, 'journalSetups');
      this.dialogRef.close();
    } else {
      return;
    }
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.journalSetupForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.journalSetupForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  save() {
    this.submitted = true;
    if (this.journalSetupForm.invalid) {
      this.alertService.alert(this.cc.texts()['overlay_journalSetupAddMandatoryAlert_title'], this.cc.texts()['overlay_journalSetupAddMandatoryAlert_content'], this.cc.texts()['screen_common_ok']);
      return;
    };
    const journalSetup: JournalSetup = this.data.mode === 'new' ? this.journalStore.getNewJournalSetup() : this.data.value;
    const updatedSetup = { ...journalSetup, ...this.journalSetupForm.value };

    if (this.data.mode === 'new') {
      this.journalStore.addJournalSetup(updatedSetup);
    } else if (this.data.mode === 'edit') {
      this.journalStore.updateJournalSetups([updatedSetup]);
    }
    this.dialogRef.close();
  }

  deleteJournalSetup() {
    this.journalStore.deleteJournalSetup([this.data.value]);
    this.dialogRef.close();
  }

}
