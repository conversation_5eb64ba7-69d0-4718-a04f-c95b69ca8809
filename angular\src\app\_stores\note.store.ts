import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from "@ngrx/signals";
import { computed, effect, inject, signal, WritableSignal } from "@angular/core";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { Note } from "@app/_interfaces/note.interface";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { DependencyService } from "@app/_services/dependency.service";
import { DatePipe } from "@angular/common";
import { UtilsService } from "@app/_services/utils.service";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { firstValueFrom } from "rxjs";
import { AttachmentType } from "@app/_types/generic.type";
import { MapService } from "@app/_services/map.service";
import { environment } from "@environments/environment";
import { Attachment } from "@app/_interfaces/generic.interface";
import { CacheService } from "@app/_services/cache.service";
import { SettingsService } from "@app/_services/settings.service";

type NoteState = {
  notes: Note[];
  myNotes: Note[];
  sharedNotes: Note[];
  isLoading: boolean;
  selectedNoteIds: string[];
};

const initialState: NoteState = {
  notes: [],
  myNotes: [],
  sharedNotes: [],
  isLoading: false,
  selectedNoteIds: []
};

export const NoteStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),

  withComputed(({ notes, selectedNoteIds }, ss = inject(SettingsService), ds = inject(DependencyService), datePipe = inject(DatePipe), mapService = inject(MapService)) => ({

    selectedNotes: computed(() => {
      const ids = selectedNoteIds();
      return new Map(
        [...ids].map(id => [id, notes().find(note => note.id === id) || null])
      );
    }),

    idToNote: computed<{ [key in string]: Note }>(() => {
      const idToNote: Record<string, Note> = {};
      notes().forEach(note => {
        idToNote[note.id] = note;
      });
      return idToNote;
    }),
  })),

  withMethods((
    store,
    idbService = inject(IndexDbService),
    ss = inject(SettingsService),
    cryptoService = inject(CryptographyService),
    firebaseFunctionService = inject(FirebaseFunctionService),
    utilsService = inject(UtilsService),
    cc = inject(CacheService)
  ) => ({
    addNote: async (noteData: Note) => {
      const note: Note = noteData;
      await idbService.add('notes', noteData);

      // Upload data to the backend
      const syncRequest = cryptoService.prepareRawData({ ...note });
      await firebaseFunctionService.uploadData(syncRequest);
    },

    updateNotes: async (notes: Note[]) => {
      const oldNotes = [];
      const newNotes = [];
      for (const note of notes) {
        const updatedAt = new Date();
        const oldNote = await firstValueFrom(idbService.getEntityById('notes', note.id));
        note.noteUpdatedAt = updatedAt;
        console.log("i am called new updated note--->>", note);
        const newNote = await idbService.update('notes', note.id, note);
        newNote!.localUpdatedAt = new Date();
        newNote!.cloudUpdatedAt = null;
        newNote!.noteUpdatedAt = updatedAt;

        oldNotes.push(oldNote);
        newNotes.push(newNote);
      }

      const syncRequest = cryptoService.preparePatchData(
        oldNotes,
        newNotes,
        FirestoreCollection.Notes
      );

      await firebaseFunctionService.uploadData(syncRequest);
    },

    deleteNote: async (notes: Note[]) => {
      const oldNotes = [];
      const newNotes = [];
      for (const note of notes) {
        note.deletedAt = new Date();
        const oldNote = await firstValueFrom(idbService.getEntityById('notes', note.id));
        const newNote = await idbService.update('notes', note.id, note);
        newNote.localUpdatedAt = new Date();
        newNote.cloudUpdatedAt = null;
        oldNotes.push(oldNote);
        newNotes.push(newNote);
      }

      const syncRequest = cryptoService.preparePatchData(
        oldNotes,
        newNotes,
        FirestoreCollection.Notes
      );

      await firebaseFunctionService.uploadData(syncRequest);
    },

    getNewNote: (): Note => {
      const user = cc.user;
      return {
        id: utilsService.getNewId(),
        title: '',
        description: '',
        attachments: [],
        emotion: null,
        uid: user?.uid ?? '',
        ownerName: user?.name,
        ownerEmail: user?.email,
        createdAt: new Date(),
        sessionId: utilsService.getNewId(),
        docVer: environment.dbVersion,
        docCollection: FirestoreCollection.Notes.toString(),
        source: 'client',
        encData: {
          dek: cryptoService.createEncryptedDocKey(),
          encFields: [
            'title',
            'description',
            'ownerName',
            'ownerEmail',
            'members.membersConfig{}.eEmail',
          ]
        },
        position: 3000,
        isPublic: false,
        publicId: null,
        members: { memberHashedEmails: [], membersConfig: {} },
        collaboratorLimit: 5,
        inviteLink: null,
        tags: [],
        isFav: false,
        lastUpdatedBy: user?.uid ?? '',
        noteUpdatedAt: new Date(),
        localUpdatedAt: new Date(),
        cloudUpdatedAt: new Date(),
        lastUpdatedAt: new Date(),
        permaDeletedAt: null,
        deletedAt: null,
      }
    },

    computeNotes: () => {
      const notes = store.notes();
      const myNotes: Note[] = [];
      const sharedNotes: Note[] = [];

      const sortedNotes = notes.sort((a, b) => {
        const aDate = a.createdAt ?? new Date();
        const bDate = b.createdAt ?? new Date();
        return aDate > bDate ? -1 : 1;
      });

      sortedNotes.forEach(note => {
        if (note.uid === cc.user.uid) {
          myNotes.push(note);
        } else {
          sharedNotes.push(note);
        }
      });

      patchState(store, { myNotes, sharedNotes });
    },

    filterNotes: (notes: Note[], searchQuery: string, isFav: boolean, moods: number[], tags: string[], attachments: AttachmentType[], startDate: Date | null, endDate: Date | null) => {
      const isMood = moods.length > 0;
      const query = searchQuery.toLowerCase();

      return notes.filter(item => {
        const matchesFav = !isFav || item.isFav === true; // Apply isFav condition if true
        const matchesMood = moods.includes(item.emotion);
        const matchesTags = tags.some(tag => item.tags?.includes(tag));
        const matchesAttachments = attachments.every(attType => item.attachments?.some((attachment: Attachment) => attachment.fileType === attType));
        const matchesDate = startDate && endDate
          ? item.noteUpdatedAt !== undefined &&
          item.noteUpdatedAt.getTime() >= startDate.getTime() &&
          item.noteUpdatedAt.getTime() <= endDate.getTime()
          : true;

        const matchesQuery = !query || item.title.toLowerCase().includes(query) || (item.description && item.description.toLowerCase().includes(query));

        return !item.deletedAt && matchesFav && matchesQuery && (isMood ? matchesMood : true) && (tags.length > 0 ? matchesTags : true) && (attachments.length > 0 ? matchesAttachments : true) && (startDate && endDate ? matchesDate : true);
      });
    },

    selectNote: (id: string) => {
      patchState(store, (state) => ({ selectedNoteIds: [...state.selectedNoteIds, id] }));
    },

    deselectNote: (id: string) => {
      patchState(store, (state) => ({ selectedNoteIds: state.selectedNoteIds.filter(i => i !== id) }));
    },
  })),

  withHooks({
    async onInit(
      store,
      idbService = inject(IndexDbService),
      cc = inject(CacheService)
    ) {

      idbService.notes$.pipe(takeUntilDestroyed()).subscribe(data => {
        if (data) {
          patchState(store, { notes: data });
          store.computeNotes();
        }
      });
    },
  }),
);
