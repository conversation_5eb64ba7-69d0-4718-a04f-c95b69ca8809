import { Pipe, PipeTransform } from '@angular/core';
import { SettingsService } from '@app/_services/settings.service';

@Pipe({
  name: 'customDate',
  standalone: true
})

export class CustomDatePipe implements PipeTransform {
  
  constructor(public ss: SettingsService) { }

  transform(value: Date | string): string {
    const date = new Date(value);
    const today = new Date();

    // Normalize both dates to midnight
    today.setHours(0, 0, 0, 0);
    date.setHours(0, 0, 0, 0);

    const isToday = date.getTime() === today.getTime();
    return isToday ? this.ss.texts()['screen_common_today'] : this.ss.getFormattedDate(date, 'd MMM yyyy');
  }
}