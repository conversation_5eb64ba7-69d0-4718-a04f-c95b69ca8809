import { Injectable } from "@angular/core";
import CryptoJS from 'crypto-js';
import * as eciesjs from "eciesjs";
import { Buffer } from 'buffer';
import { AngularFireStorage } from "@angular/fire/compat/storage";
import { firstValueFrom } from "rxjs";
import { UtilsService } from "./utils.service";
import { DataPatch, DataSyncRequestModel, DataToSync } from "@app/_interfaces/encryption-data.interface";
import * as jsonpatch from 'fast-json-patch';
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { StorageService } from "./storage.servive";
import { CacheService } from "./cache.service";
@Injectable({
  providedIn: 'root',
})

export class CryptographyService {

  readonly userSecretKey = 'user_secret_key';
  readonly eccPublicKey = 'ecc_public_key';
  readonly eccPrivateKey = 'ecc_private_key';
  readonly eccEncPrivateKey = 'ecc_enc_private_key';

  aesSecretKey: string | null = null;
  eccPublicKeySPKI: string | null = null;
  eccPrivateKeyPKCS8: string | null = null;
  eccEncPrivateKeyPKCS8: string | null = null;

  constructor(
    private storage: AngularFireStorage,
    private utilsService: UtilsService,
    private storageService: StorageService,
    private cc: CacheService,
  ) {

  }

  // Encrypt the text data
  private encryptValue(data: string, ddk: string): string {
    if (!data || data.startsWith("encAes_")) {
      throw new Error("Data is invalid or already encrypted");
    }

    // Create the AES encryptor using the document decrypted key
    const encryptor = this.getAESEncryptor(ddk);

    // Encrypt the data
    const encryptedData = encryptor.encrypt(data);

    // Combine IV and ciphertext into a single Uint8Array
    const ivArrayBuffer = this.base64Decode(encryptedData.iv);
    const ciphertextArrayBuffer = this.base64Decode(encryptedData.ciphertext);

    const encryptedDataWithIV = new Uint8Array([
      ...new Uint8Array(ivArrayBuffer),
      ...new Uint8Array(ciphertextArrayBuffer),
    ]);

    // Encode the combined result as Base64 and prefix with "encAes_"
    return `encAes_${this.encodeToBase64(encryptedDataWithIV)}`;
  }

  // Decrypt the text data
  decryptValue(data: string, ddk: string): string | null {
    if (!data || !data.startsWith("encAes_")) {
      console.log('Data is invalid or already decrypted-->>>', data);
      // throw new Error("Data is invalid or already decrypted");
      return data;
    }
    try {
      // Create the AES decryptor using the document decrypted key
      const encryptor = this.getAESEncryptor(ddk);

      // Decode the base64 string to Uint8Array
      const encryptedDataWithIV = this.base64Decode(data.substring(7));  // Assuming the prefix is '-----'

      // Separate the IV and encrypted data
      const iv = encryptedDataWithIV.slice(0, 16);
      const encryptedData = encryptedDataWithIV.slice(16);

      // Convert the encrypted data to base64 string
      const encryptedDataBase64 = this.arrayBufferToBase64(encryptedData);

      // Convert the IV to base64 string
      const ivBase64 = this.arrayBufferToBase64(iv);

      // Decrypt the data
      const decryptedData = encryptor.decrypt(encryptedDataBase64, ivBase64);

      // Check if decryptedData is a string or bytes (Uint8Array)
      let decryptedText: string;

      // Decrypted data is already a string
      decryptedText = decryptedData;

      return decryptedText;

    } catch (error) {
      console.error('Error decrypting and saving private key:', error);

      return null;
    }
  }

  decryptCustomEncryptedData(data: string, ddk: string): string | null {
    try {
      // Create the AES decryptor using the document decrypted key
      const encryptor = this.getAESEncryptor(ddk);

      // Decode the base64 string to Uint8Array
      const encryptedDataWithIV = this.base64Decode(data.substring(7));  // Assuming the prefix is '-----'

      // Separate the IV and encrypted data
      const iv = encryptedDataWithIV.slice(0, 16);
      const encryptedData = encryptedDataWithIV.slice(16);

      // Convert the encrypted data to base64 string
      const encryptedDataBase64 = this.arrayBufferToBase64(encryptedData);

      // Convert the IV to base64 string
      const ivBase64 = this.arrayBufferToBase64(iv);

      // Decrypt the data
      const decryptedData = encryptor.decrypt(encryptedDataBase64, ivBase64);

      // Check if decryptedData is a string or bytes (Uint8Array)
      let decryptedText: string;

      // Decrypted data is already a string
      decryptedText = decryptedData;

      return decryptedText;

    } catch (error) {
      console.error('Error decrypting and saving private key:', error);

      return null;
    }
  }

  public encryptFile(data: Uint8Array, dek: string): Uint8Array | null {
    try {
      if (!dek) {
        console.error('Invalid decryption key: Key is missing');
        return null;
      }

      const ddk = this.getDecryptedDocKey(dek);

      if (!ddk) {
        throw new Error('Failed to decrypt DEK.');
      }

      // Create the AES encryptor using the document decrypted key
      const encryptor = this.getAESEncryptor(ddk);

      return encryptor.encryptBytes(data);
    } catch (error) {
      console.error('Error encrypting file:', error);
      return null;
    }
  }

  public decryptFile(data: Uint8Array, dek: string): Uint8Array | null {
    try {
      // Validate inputs
      if (!data || data.length <= 16) {
        console.error('Invalid encrypted data: Data is missing or too short');
        return null;
      }

      if (!dek) {
        console.error('Invalid decryption key: Key is missing');
        return null;
      }

      const ddk = this.getDecryptedDocKey(dek);

      if (!ddk) {
        throw new Error('Failed to decrypt DEK.');
      }

      // Extract IV from the first 16 bytes
      const iv = data.slice(0, 16);

      // Get the encrypted data (everything after the IV)
      const encryptedData = data.slice(16);

      // Get the AES encryptor with the provided key
      const encryptor = this.getAESEncryptor(ddk);

      // Decrypt the data
      const decryptedData = encryptor.decryptBytes(encryptedData, iv);

      return decryptedData;
    } catch (error) {
      console.error('Error decrypting file:', error);
      return null;
    }
  }

  // Helper method to decode base64 string into Uint8Array
  private base64Decode(base64: string): Uint8Array {
    const binaryString = atob(base64);  // Decode base64 to binary string
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  // Helper method to convert a Uint8Array to a base64 string
  private arrayBufferToBase64(buffer: Uint8Array): string {
    let binary = '';
    const len = buffer.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(buffer[i]);
    }
    return btoa(binary);  // Convert binary string to base64
  }

  // Helper method to encode to base64
  private encodeToBase64(arrayBuffer: ArrayBuffer): string {
    const bytes = new Uint8Array(arrayBuffer);
    let binary = "";
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  // Get AES encryptor
  private getAESEncryptor(base64HexKey: string) {
    const key = CryptoJS.enc.Base64.parse(base64HexKey);

    return {
      encrypt: (data: string) => {
        const iv = CryptoJS.lib.WordArray.random(16);
        const encrypted = CryptoJS.AES.encrypt(data, key, {
          iv: iv,
          padding: CryptoJS.pad.Pkcs7,
        });
        return {
          ciphertext: encrypted.ciphertext.toString(CryptoJS.enc.Base64),
          iv: iv.toString(CryptoJS.enc.Base64),
        };
      },
      decrypt: (ciphertext: string, ivBase64: string) => {
        const iv = CryptoJS.enc.Base64.parse(ivBase64);
        const decrypted = CryptoJS.AES.decrypt(
          ciphertext,
          key,
          { iv: iv, padding: CryptoJS.pad.Pkcs7 }
        );
        return decrypted.toString(CryptoJS.enc.Utf8);
      },
      encryptBytes: (decryptedBytes: Uint8Array) => {
        // Convert Uint8Array to WordArray
        const dataWords = CryptoJS.lib.WordArray.create(decryptedBytes as any);

        // Generate a random IV or use the provided one
        const iv = CryptoJS.lib.WordArray.random(16);

        // Encrypt the data
        const encrypted = CryptoJS.AES.encrypt(
          dataWords,
          key,
          {
            iv: iv,
            padding: CryptoJS.pad.Pkcs7,
            mode: CryptoJS.mode.CBC
          }
        );

        // Convert the encrypted data to Uint8Array
        const encryptedBytes = this.base64ToUint8Array(encrypted.ciphertext.toString(CryptoJS.enc.Base64));
        const ivBytes = this.base64ToUint8Array(iv.toString(CryptoJS.enc.Base64));

        // Combine IV and encrypted data into a single Uint8Array
        const result = new Uint8Array(ivBytes.length + encryptedBytes.length);
        result.set(ivBytes, 0);
        result.set(encryptedBytes, ivBytes.length);

        return result;
      },
      decryptBytes: (encryptedBytes: Uint8Array, iv: Uint8Array) => {
        // Convert Uint8Array to WordArray
        const encryptedWords = CryptoJS.lib.WordArray.create(encryptedBytes as any);
        const ivWords = CryptoJS.lib.WordArray.create(iv as any);

        // Create CipherParams object
        const cipherParams = CryptoJS.lib.CipherParams.create({
          ciphertext: encryptedWords
        });

        // Decrypt
        const decrypted = CryptoJS.AES.decrypt(
          cipherParams,
          key,
          {
            iv: ivWords,
            padding: CryptoJS.pad.Pkcs7
          }
        );

        // Convert WordArray to Uint8Array
        const words = decrypted.words;
        const sigBytes = decrypted.sigBytes;
        const result = new Uint8Array(sigBytes);
        let offset = 0;

        for (let i = 0; i < sigBytes; i += 4) {
          const word = words[i >>> 2];
          if (word === undefined) continue;

          result[offset++] = (word >>> 24) & 0xff;
          if (offset < sigBytes) result[offset++] = (word >>> 16) & 0xff;
          if (offset < sigBytes) result[offset++] = (word >>> 8) & 0xff;
          if (offset < sigBytes) result[offset++] = word & 0xff;
        }

        return result;
      }
    };
  }

  base64ToUint8Array(base64: string): Uint8Array {
    const binaryString = atob(base64);  // Decode base64 to binary string
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  };

  // Get new DEK
  private getNewDEK() {
    const bytesView = CryptoJS.lib.WordArray.random(32);

    return bytesView.toString(CryptoJS.enc.Base64);
  }

  // Helper method to convert PEM to HEX
  public pemToHex(pem: string, isPublicKey: boolean): string {
    if (isPublicKey) {
      // Remove headers, footers, and newlines to get the base64-encoded part.
      const base64Content = pem
        .replace(/-----BEGIN PUBLIC KEY-----/, '')
        .replace(/-----END PUBLIC KEY-----/, '')
        .replace(/\n/g, '');

      // Decode the base64 content into a binary string
      const binaryString = atob(base64Content);

      // Convert the binary string to a Uint8Array
      const derBuffer = new Uint8Array(
        binaryString.split('').map((char) => char.charCodeAt(0))
      );

      // Convert the Uint8Array to a hexadecimal string
      const derHex = Array.from(derBuffer)
        .map((byte) => byte.toString(16).padStart(2, '0'))
        .join('');

      // Find the start index of the public key
      const publicKeyStart = derHex.indexOf('03420004') + 8; // Offset 8 for '03420004'

      return derHex.substring(publicKeyStart);
    } else {
      // Remove headers, footers, and newlines to get the base64-encoded part.
      const base64Content = pem
        .replace(/-----BEGIN PRIVATE KEY-----/, '')
        .replace(/-----END PRIVATE KEY-----/, '')
        .replace(/\n/g, '');

      // Decode the base64 content into a binary string
      const binaryString = atob(base64Content);

      // Convert the binary string to a Uint8Array
      const derBuffer = new Uint8Array(
        binaryString.split('').map((char) => char.charCodeAt(0))
      );

      // Convert the Uint8Array to a hexadecimal string
      const derHex = Array.from(derBuffer)
        .map((byte) => byte.toString(16).padStart(2, '0'))
        .join('');

      // Extract the private key bytes from DER hex string
      const privateKeyStart = derHex.indexOf('0201010420') + 10; // Offset 10 for '0201010420'
      // 32 bytes (64 hex characters) for the private key
      return derHex.substring(
        privateKeyStart,
        privateKeyStart + 64
      );
    }
  }

  // Create a new encrypted doc key
  public createEncryptedDocKey(): string {
    const dek = this.getNewDEK();

    return this.getEncryptedDocKey(dek);
  }

  // Encrypt doc key
  public getEncryptedDocKey(docKey: string, userSecret: string | null = this.eccPublicKeySPKI) {
    if (docKey.startsWith("encEcc_")) {
      throw new Error("Document key is already encrypted");
    }

    const data = Buffer.from(docKey, "base64");
    const encryptedData = eciesjs.encrypt(this.pemToHex(userSecret!, true), data);
    return "encEcc_" + encryptedData.toString("base64");
  }

  // Get Decrypted document key
  public getDecryptedDocKey(docKey: string) {
    if (docKey.startsWith("encEcc_")) {
      let decryptedUserPrivateKey = this.eccPrivateKeyPKCS8;

      if (decryptedUserPrivateKey == null) {
        throw new Error("User private key not found");
      }
      const encryptedBuffer = Buffer.from(docKey.substring(7), "base64");
      const decryptedData = eciesjs.decrypt(
        this.pemToHex(decryptedUserPrivateKey, false),
        encryptedBuffer
      );
      return decryptedData.toString("base64");
    } else {
      throw new Error("Invalid encrypted doc key");
    }
  }

  public encryptValueWithDek(value: string, dek: string) {
    const ddk = this.getDecryptedDocKey(dek);
    return this.encryptValue(value, ddk);
  }

  public reEncryptDocEncKey(dek: string, publicKey: string) {
    const docDecryptedKey = this.getDecryptedDocKey(dek);
    const docEncryptedKey = this.getEncryptedDocKey(docDecryptedKey, publicKey);
    return docEncryptedKey;
  }

  // Decrypt
  public decrypt(document: any, isEncrypting: boolean = false, isCollab: boolean = false): any {
    const encData = document.encData;

    if ((!isCollab && (!encData || !encData.dek || !encData.encFields)) || (isCollab && !document.members.membersConfig[this.storageService.getHashedEmail()])) {
      return document; // Return as-is if no encryption data is found
    }

    const ddk = isCollab ? this.getDecryptedDocKey(document.members.membersConfig[this.storageService.getHashedEmail()].dek) : this.getDecryptedDocKey(encData.dek);

    if (!ddk) {
      throw new Error('Failed to decrypt DEK.');
    }

    encData.encFields.forEach((fieldPath: string) => {
      const keys = fieldPath.split('.');

      // Handle dynamic key fields (e.g., listItems{}.item)
      if (fieldPath.includes('{}')) {
        const [parentPath, nestedKey] = fieldPath.split('{}');
        const parentObject = this.getValueByPath(document, parentPath);

        const notToDecryptPaths = ['members.membersConfig']

        if (parentObject && typeof parentObject === 'object') {
          Object.keys(parentObject).forEach((dynamicKey) => {
            const nestedObject = parentObject[dynamicKey];
            const nestedField = nestedKey.slice(1); // Remove the leading '.' from nestedKey

            if (nestedObject && nestedObject[nestedField]) {
              nestedObject[nestedField] = isEncrypting
                ? this.encryptValue(nestedObject[nestedField], ddk)
                : this.decryptValue(nestedObject[nestedField], ddk);
            }
          });
        }
      }
      // Handle array fields (e.g., listItems[].item)
      else if (fieldPath.includes('[]')) {
        const arrayFieldPath = keys.slice(0, -1).join('.');
        const arrayField = this.getValueByPath(document, arrayFieldPath.split('[]')[0]);

        if (Array.isArray(arrayField)) {
          arrayField.forEach((item) => {
            const encKey = keys[keys.length - 1];

            if (item[encKey]) {
              item[encKey] = isEncrypting
                ? this.encryptValue(item[encKey], ddk)
                : this.decryptValue(item[encKey], ddk);
            }
          });
        }
      }
      // Handle root-level fields (e.g., title, description)
      else if (keys.length === 1) {
        const fieldName = keys[0];
        if (document[fieldName]) {
          document[fieldName] = isEncrypting
            ? this.encryptValue(document[fieldName], ddk)
            : this.decryptValue(document[fieldName], ddk);
        } else {
          console.warn(`Field ${fieldName} not found at root level`);
        }
      }
      // Handle nested fields (e.g., members.membersConfig{}.eEmail)
      else {
        const encFieldPath = keys.join('.');
        const encryptedValue = this.getValueByPath(document, encFieldPath);

        if (encryptedValue) {
          const parentPath = keys.slice(0, -1).join('.');
          const fieldName = keys[keys.length - 1];
          const parentObject = this.getValueByPath(document, parentPath);

          if (!parentObject) {
            console.error(`Parent object not found for path: ${parentPath}`);
            return;
          }

          if (!parentObject[fieldName]) {
            console.warn(`Field ${fieldName} not found in parent object`);
            return;
          }

          parentObject[fieldName] = isEncrypting
            ? this.encryptValue(encryptedValue, ddk)
            : this.decryptValue(encryptedValue, ddk);
        }
      }
    });

    return document;
  }

  // Get value by path
  public getValueByPath(obj: any, path: string): any {
    return path ?
      path.split('.').reduce((o, key) => {
        return o?.[key];
      }, obj)
      : obj; // Return the root object if the path is empty
  }

  public async decryptBytesInMemory(
    fileUrl: string,
    encryptedSecret: string,
    chunkSize: number
  ): Promise<Uint8Array | null> {
    try {
      // Step 1: Get the download URL
      const ref = this.storage.refFromURL(fileUrl);
      const downloadUrl = await firstValueFrom(ref.getDownloadURL());

      // Step 2: Fetch encrypted data
      const response = await fetch(downloadUrl);
      const arrayBuffer = await response.arrayBuffer();
      const encryptedData = new Uint8Array(arrayBuffer);

      // Step 3: Decrypt the secret key
      const decryptedDek = this.getDecryptedDocKey(encryptedSecret);
      if (!decryptedDek) {
        console.error('Decrypted DEK is null');
        return null;
      }

      // Step 4: Decrypt data in chunks
      let iv: Uint8Array | null = null;
      const decryptedChunks: Uint8Array[] = [];

      for (let i = 0; i < encryptedData.length; i += chunkSize) {
        let chunk = encryptedData.slice(i, i + chunkSize);

        if (!iv) {
          // Extract IV from the first chunk
          iv = chunk.slice(0, 16);
          chunk = chunk.slice(16);
        }

        const decryptedChunk = this.decryptChunk(chunk, decryptedDek, iv);
        decryptedChunks.push(decryptedChunk);
      }

      // Combine all chunks into a single Uint8Array
      const totalLength = decryptedChunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of decryptedChunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return result;
    } catch (error) {
      console.error('Error decrypting bytes in memory:', error);
      return null;
    }
  }

  hashDataInBase64(data: string, algorithm: string = 'sha256'): string {
    const utf8Encoded = CryptoJS.enc.Utf8.parse(data);
    let hash: CryptoJS.lib.WordArray;

    switch (algorithm.toLowerCase()) {
      case 'sha256':
        hash = CryptoJS.SHA256(utf8Encoded);
        break;
      case 'sha512':
        hash = CryptoJS.SHA512(utf8Encoded);
        break;
      case 'md5':
        hash = CryptoJS.MD5(utf8Encoded);
        break;
      default:
        throw new Error(`Unsupported algorithm: ${algorithm}`);
    }

    return CryptoJS.enc.Base64.stringify(hash);
  }

  private decryptChunk(
    encryptedChunk: Uint8Array,
    decryptedDek: string,
    iv: Uint8Array
  ): Uint8Array {
    try {
      // Use AES decryption with the provided DEK
      const aesDecryptor = this.getAESEncryptor(decryptedDek);

      // Decrypt directly to Uint8Array without string conversion
      const decryptedData = aesDecryptor.decryptBytes(encryptedChunk, iv);
      return new Uint8Array(decryptedData);
    } catch (error) {
      console.error('Error decrypting chunk:', error);
      return new Uint8Array();
    }
  }

  public prepareRawData(data: any): DataSyncRequestModel {
    const decryptedList = this.decrypt(data, true, this.cc.user.uid !== data.uid);

    const dataSync: DataToSync = {
      id: decryptedList.id,
      updateType: 'raw',
      localUpdatedAt: decryptedList.localUpdatedAt,
      data: JSON.stringify(decryptedList),
    };

    return { dataList: [dataSync] };
  }

  public preparePatchData(
    oldDatas: any[],
    newDatas: any[],
    docCollection: FirestoreCollection,
  ): DataSyncRequestModel {
    const dataList: Record<string, any>[] = [];

    for (let i = 0; i < oldDatas.length; i++) {
      const oldData = oldDatas[i];
      const newData = newDatas[i];

      const newENCData = this.decrypt(newData, true, this.cc.user.uid !== newData.uid);
      const oldENCData = this.decrypt(oldData, true, this.cc.user.uid !== oldData.uid);

      console.log({ oldENCData, newENCData });

      const cleanOld = this.normalizeDates(oldENCData);
      const cleanNew = this.normalizeDates(newENCData);
      const patch = jsonpatch.compare(cleanOld, cleanNew);

      console.log({ patch });

      const patchData: DataPatch = {
        patchId: this.utilsService.getNewId(),
        id: newData.id,
        docVer: oldData.docVer,
        docCollection: docCollection,
        uid: oldData.uid,
        localUpdatedAt: newData.localUpdatedAt,
        sessionId: this.utilsService.getNewId(),
        patch: JSON.stringify(patch),
      }

      const dataSync: DataToSync = {
        id: patchData.patchId,
        updateType: 'patch',
        localUpdatedAt: patchData.localUpdatedAt,
        data: JSON.stringify(patchData),
      };

      dataList.push(dataSync);
    }

    return { dataList };
  }

  normalizeDates = (obj: any): any => {
    if (obj instanceof Date) {
      return obj.toISOString();
    }
    if (Array.isArray(obj)) {
      return obj.map(this.normalizeDates);
    }
    if (typeof obj === 'object' && obj !== null) {
      const normalized: any = {};
      for (const key of Object.keys(obj)) {
        normalized[key] = this.normalizeDates(obj[key]);
      }
      return normalized;
    }
    return obj;
  }
}
