import { CommonModule } from '@angular/common';
import { Component, computed, Inject, inject, Signal, signal, ViewChild, WritableSignal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AlertService } from '@app/_services/alert.service';
import { InputEmailComponent } from '@app/components/shared/inputs/input-email/input-email.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { Subject, takeUntil } from 'rxjs';
import * as _ from "lodash";
import { CacheService } from '@app/_services/cache.service';
import { CollaboraterRole, CollaboraterStatus } from '@app/_types/generic.type';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { emailValidator } from '@app/_directives/form-validator.directive';
import { Collaborators } from '@app/_interfaces/generic.interface';
import { Collection } from '@app/_types/collection.type';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-collaborator-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputEmailComponent,
    InputDropdownComponent
  ],
  templateUrl: './collaborator-form.component.html',
  styleUrl: './collaborator-form.component.scss'
})

export class CollaboratorFormComponent {

  @ViewChild('collabForm') collabForm!: NgForm;
  unSubscribe = new Subject<void>();
  collaboratorForm: FormGroup;
  collaboratorInitial: any;
  roleSignal: WritableSignal<CollaboraterRole> = signal('viewer');
  members: WritableSignal<Collaborators> = signal({ memberHashedEmails: [], membersConfig: {} });
  existingMemberEmails: Signal<string[]> = computed(() => {
    return this.members().memberHashedEmails.map((email: string) => this.members().membersConfig[email].eEmail);
  });
  emailToStatusMap: Signal<Record<string, CollaboraterStatus>> = computed(() => {
    const map: Record<string, CollaboraterStatus> = {};
    const allHashedEmails = Object.keys(this.members().membersConfig);
    allHashedEmails.forEach((hashedEmail: string) => {
      const email = this.members().membersConfig[hashedEmail].eEmail;
      map[email] = this.members().membersConfig[hashedEmail].status;
    });
    return map;
  });
  collectionToBlockContentMap: Signal<{ [key: Collection | string]: string }> = signal({
    'todos': 'overlay_memberBlocked_todoContent',
    'lists': 'overlay_memberBlocked_listContent',
    'notes': 'overlay_memberBlocked_noteContent',
    'habitSetups': 'overlay_memberBlocked_habitContent',
    'journalSetups': 'overlay_memberBlocked_journalContent',
    'moneyTrackerSetups': 'overlay_memberBlocked_moneyTrackerContent',
  });

  constructor(
    public dialogRef: MatDialogRef<CollaboratorFormComponent>,
    private alertService: AlertService,
    private fb: FormBuilder,
    public cc: CacheService,
    public vs: ViewSettingService,
    public ss: SettingsService,
    @Inject(MAT_DIALOG_DATA) public data: { members: Collaborators, collection: Collection }
  ) {
    this.members.set(this.data.members);

    this.collaboratorInitial = this.initiateForm();
    this.roleSignal.set(this.collaboratorInitial.role);

    this.collaboratorForm = this.fb.group({
      email: new FormControl(this.collaboratorInitial.email, [Validators.required, emailValidator(), Validators.maxLength(320)]),
      role: new FormControl(this.collaboratorInitial.role, Validators.required)
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  getFc(fcName: string): FormControl {
    return this.collaboratorForm.get(fcName) as FormControl;
  }

  initiateForm(): any {
    return {
      email: '',
      role: 'viewer',
    };
  }

  hasChanges() {
    const initial = _.cloneDeep(this.collaboratorInitial);
    const current = _.cloneDeep(this.collaboratorForm.value);
    return !_.isEqual(initial, current);
  }

  async save() {
    if (this.existingMemberEmails().includes(this.collaboratorForm.value.email)) {
      await this.alertService.alert(this.cc.texts()['overlay_memberAlreadyExists_title'], this.cc.texts()['overlay_memberAlreadyExists_content'], this.cc.texts()['screen_common_ok']);
      return;
    }
    if (this.collaboratorForm.value.email === this.cc.user.email) {
      await this.alertService.alert(this.cc.texts()['overlay_selfInviteDenied_title'], this.cc.texts()['overlay_selfInviteDenied_content'], this.cc.texts()['screen_common_ok']);
      return;
    }
    if (this.existingMemberEmails().length >= 10) {
      await this.alertService.alert(this.cc.texts()['overlay_memberLimitReached_title'], this.cc.texts()['overlay_memberLimitReached_content'], this.cc.texts()['screen_common_ok']);
      return;
    }
    const collabStatus: CollaboraterStatus = this.emailToStatusMap()[this.collaboratorForm.value.email] || 'active';
    if (collabStatus === 'blocked') {
      await this.alertService.alert(this.cc.texts()['overlay_memberBlocked_title'], this.cc.texts()[this.collectionToBlockContentMap()[this.data.collection]], this.cc.texts()['screen_common_ok']);
      return;
    }
    this.collaboratorForm.get('role')?.setValue(this.roleSignal());
    this.dialogRef.close(this.collaboratorForm.value);
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.collaboratorForm.reset();
    this.collabForm.resetForm();
    this.collaboratorForm.patchValue(this.collaboratorInitial);
  }

}
