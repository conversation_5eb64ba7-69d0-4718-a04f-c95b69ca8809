import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AlertDialogComponent, AlertDialogModel } from '@app/components/shared/alert-dialog/alert-dialog.component';
import { SnackBarComponent, SnackBarModel } from '@app/components/shared/snack-bar/snack-bar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SettingsService } from './settings.service';

@Injectable({
  providedIn: 'root',
})

export class AlertService {

  private alertDialog: any;
  constructor(public dialog: MatDialog, public ss: SettingsService, private snackBar: MatSnackBar) { }

  alert(
    header: string | { var: string; eg: string; }[] = '',
    message: string | { var: string; eg: string; }[] = '',
    yesButtonText: string | { var: string; eg: string; }[] = 'Yes',
    yesButtonColor: string = 'color-35',
    secondaryMessage: string | { var: string; eg: string; }[] | null = null,
    secondaryMessageColor: string = 'color-11'
  ): Promise<any> {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      width: '100%',
      maxWidth: '400px',
      disableClose: false,
      data: new AlertDialogModel(
        header instanceof Array ? this.ss.textsWithVariables(header) : header,
        message instanceof Array ? this.ss.textsWithVariables(message) : message,
        false,
        false,
        '',
        { yes: yesButtonText instanceof Array ? this.ss.textsWithVariables(yesButtonText) : yesButtonText, no: '', yesButtonColor: yesButtonColor, noButtonColor: 'color-7' },
        false,
        secondaryMessage instanceof Array ? this.ss.textsWithVariables(secondaryMessage) : secondaryMessage,
        secondaryMessageColor
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }

  confirm(
    header: string | { var: string; eg: string; }[] = '',
    message: string | { var: string; eg: string; }[] = '',
    yesButtonText: string | { var: string; eg: string; }[] = 'Yes',
    noButtonText: string | { var: string; eg: string; }[] = 'No',
    yesButtonColor: string = 'color-35',
    noButtonColor: string = 'color-7'
  ) {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      width: '100%',
      maxWidth: '400px',
      disableClose: false,
      data: new AlertDialogModel(
        header instanceof Array ? this.ss.textsWithVariables(header) : header,
        message instanceof Array ? this.ss.textsWithVariables(message) : message,
        false,
        false,
        '',
        { yes: yesButtonText instanceof Array ? this.ss.textsWithVariables(yesButtonText) : yesButtonText, no: noButtonText instanceof Array ? this.ss.textsWithVariables(noButtonText) : noButtonText, yesButtonColor: yesButtonColor, noButtonColor: noButtonColor },
        true
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }

  checkAndConfirm(
    header: string,
    message: string,
    checkboxChecked: boolean = false,
    checkboxLabel: string,
    yesButtonText = 'Yes',
    noButtonText = 'No'
  ) {
    this.alertDialog = this.dialog.open(AlertDialogComponent, {
      minWidth: '500px',
      maxWidth: '500px',
      data: new AlertDialogModel(
        header,
        message,
        true,
        checkboxChecked,
        checkboxLabel,
        { yes: yesButtonText, no: noButtonText, yesButtonColor: 'color-35', noButtonColor: 'color-7' },
        true,
      ),
    });
    if (!confirm) {
      return Promise.resolve(false);
    }
    return Promise.resolve(this.alertDialog.afterClosed().toPromise());
  }

  toast(message: string, primaryActionText: string = 'screen_common_dismiss', secondaryActionText: string = ''): Promise<boolean> {
    const duration = 5000;
    let resolveFn: (value: boolean) => void;

    const resultPromise = new Promise<boolean>((resolve) => {
      resolveFn = resolve;
    });
    const snackbar = this.snackBar.openFromComponent(SnackBarComponent, {
      duration: duration,
      data: new SnackBarModel(
        message,
        primaryActionText,
        secondaryActionText,
        () => { resolveFn(true); },
        () => { resolveFn(false); }
      ),
      panelClass: ['custom-snackbar'],
      horizontalPosition: 'left',
      verticalPosition: 'bottom',
    });

    setTimeout(() => resolveFn(false), 5000);

    return resultPromise;
  }
}
