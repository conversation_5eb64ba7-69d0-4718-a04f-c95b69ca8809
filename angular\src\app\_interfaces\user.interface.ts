import { FieldValue, Timestamp } from "@angular/fire/firestore";
import { EncryptionData, CustomDate } from "./generic.interface";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { PublicNote } from "./note.interface";
import { PublicList } from "./list.interface";
import { AppThemeType, Language } from "@app/_types/generic.type";
export interface FbUser {
  id: string,
  name: string,
  email: string,
  domain: string,
  verifiedEmail: boolean,
  roles: string[],
  picture?: string,
  uid?: string,
  createdBy?: string,
  createdAt?: Timestamp | null,
  updatedAt?: Timestamp | null,
  cloudUpdatedAt?: Timestamp | null;
  deletedAt?: Timestamp | null
}

export interface JsUser {
  id: string,
  name: string,
  email: string,
  domain: string,
  verifiedEmail: boolean,
  roles: string[],
  picture?: string,
  uid?: string,
  createdBy?: string,
  createdAt?: Date | null,
  updatedAt?: Date | null,
  cloudUpdatedAt?: Date | FieldValue | null;
  localUpdatedAt?: Date | FieldValue | null;
  lastUpdatedAt?: Date | FieldValue | null;
  deletedAt?: Date | null
}

export interface StringUser {
  id: string,
  name: string,
  email: string,
  domain: string,
  verifiedEmail: boolean,
  roles: string[],
  picture?: string,
  uid?: string,
  createdBy?: string,
  createdAt?: Date | null,
  updatedAt?: Date | null,
  cloudUpdatedAt?: Date | null;
  deletedAt?: Date | null
}

export interface UserData {
  uid: string,
  email: string,
  name: string,
  isNewUser: boolean,
  hashedEmail: string,
  pseudoName: string
}
export interface User {
  id: string,
  docVer: number,
  docCollection: FirestoreCollection.Users,
  uid: string,
  createdAt: Date,
  encData: EncryptionData,
  securitySettings: {
    passcode: string | null,
    isPasscodeEnabled: boolean,
    isBiometricEnabled: boolean
  },
  dataBackupInfo: DataBackupInfo,
  muid: string | null,
  permaDeletedAt: Date | null,
  deletedAt: Date | null,
  subscriptionInfo: SubscriptionInfo,
  source: string,
  sessionId: string,
  userInfo: UserInfo,
  cloudUpdatedAt: Date,
  localUpdatedAt: Date
}

export interface PublicUser {
    id: string,
    name: string,
    mevolveId: string,
    createdAt: Date,
    updatedAt: Date,
    notes: PublicNote[],
    lists: PublicList[],
    followingCount: number,
    followersCount: number,
    bio: string,
    profileTags: string[],
    socialLinks: [],
    uid: string
}

export interface DataBackupInfo {
  export: {
    status: string;
    lastUpdatedAt: Date | null;
  },
  import: {
    status: string;
    lastUpdatedAt: Date | null;
  }
}

export interface UserInfo {
  name: string;
  email: string;
  pseudoName: string;
  resetPasscode: boolean;
  storageUsed: number;
  isUsingCustomKey: boolean;
  tokensValidAfterTime: Date | null;
  createdAt: Date;
  deletedAt: Date | null;
}

export interface SubscriptionInfo {
  entitlement: 'free' | 'basic' | 'pro' | 'plus';
  subscriptionExpDate: Date | null;
  subscriptionStartDate: Date | null;
  subscriptionState: "none" | "subscribed" | "subscriptionExpired";
  subscriptionType: "custom" | "monthly" | "yearly";
  storeType: string | null;
  productId: string | null;
  unsubscribedAt: Date | null;
}

export interface ProviderData {
  providerId: string;
  uid: string;
  displayName: string;
  email: string;
  phoneNumber: string | null;
  photoURL: string;
}

export interface STSTokenManager {
  refreshToken: string;
  accessToken: string;
  expirationTime: number;
}

export interface UserResource {
  id: string;
  uid: string;
  favorites: string[];
  public: string[];
  shared: {
    sharedLists: string[];
    sharedNotes: string[];
    sharedTodos: string[];
    sharedMoneyTrackerSetups: string[];
    sharedHabitSetups: string[];
    sharedJournalSetups: string[];
  };
  tags: Hashtag[];
  encData?: EncryptionData;
  encryptionData?: EncryptionData;
  docVer?: number;
  docCollection?: string;
  source?: string;
  sessionId?: string;
  createdAt?: Date;
  cloudUpdatedAt?: Date | FieldValue | null;
  localUpdatedAt?: Date;
  lastUpdatedAt?: Date;
  deletedAt?: Date;
  permaDeletedAt?: Date;
}

export interface UserMetaData {
  id: string;
  uid: string;
  docVer?: number;
  docCollection?: string;
  encData?: EncryptionData;
  encryptionData?: EncryptionData;
  source?: string;
  appSettings: AppSettings,
  featureSettings: {
    hideCompletedItems: boolean;
    showFeatureLabels: boolean;
    showUserGoal: boolean;
    userGoal: string;
    viewType: string;
    showTimebox: boolean;
    showCalendarView: boolean;
    showListFeature: boolean;
    showNoteFeature: boolean;
    showTodoFeature: boolean;
  };
  featureUsageInfo: {
    todosCount: number;
    todosUpdatedAt: Date | null;
    notesCount: number;
    notesUpdatedAt: Date | null;
    listsCount: number;
    listsUpdatedAt: Date | null;
    habitActionsCount: number;
    habitActionsUpdatedAt: Date | null;
    habitSetupsCount: number;
    habitSetupsUpdatedAt: Date | null;
    journalActionsCount: number;
    journalActionsUpdatedAt: Date | null;
    journalSetupsCount: number;
    journalSetupsUpdatedAt: Date | null;
  };
  dataBackupInfo: {
    export: {
      status: string;
      lastUpdatedAt: Date | null;
    },
    import: {
      status: string;
      lastUpdatedAt: Date | null;
    }
  },
  chatStatus: string | null;
  reportStatus: string | null;
  isFeedbackGiven: boolean;
  isStoreFeedbackGiven: boolean;
  muid: string | null;
  userInfo: {
    name: string;
    email: string;
    pseudoName: string | null;
    createdAt: Date | null;
    isUsingCustomKey: boolean;
    resetPasscode: boolean;
    storageUsed: number;
    tokensValidAfterTime: Date | null;
  };
  userSegments: string[];
  notificationSettings: {
    emailNotificationTime: CustomDate;
    emailNotificationTimezone: string;
    isDailyAgendaEmailNotificationEnabled: boolean;
    isDailyAgendaMobileNotificationEnabled: boolean;
    isDailyAgendaTmzDependent: boolean;
    isOverdueEmailNotificationEnabled: boolean;
    isMuteAllDeviceTmzDependent: boolean;
    mobileDailyAgendaNotificationTime: CustomDate;
    mobileSnoozeType: string;
    mobileSoundType: string;
    muteAllCustomPresetSelected: boolean;
    muteAllDevice: null;
    muteAllDeviceStartAt: Date | null;
    pinReminder: boolean;
    remindMeType: string;
  };
  securitySettings: {
    isBiometricEnabled: boolean;
    isPasscodeEnabled: boolean;
    passcode: string | null;
  },
  subscriptionInfo: {
    entitlement: string;
    subscriptionExpDate: Date | null;
    subscriptionStartDate: Date | null;
    subscriptionState: string;
    subscriptionType: string;
    storeType: string | null;
    productId: string | null;
    unsubscribedAt: Date | null;
  };
  superSubscription: string | null;
  userDeletedStatus: string | null;
  sessionId?: string;
  createdAt?: Date;
  cloudUpdatedAt?: Date | FieldValue | null;
  deletedAt?: Date;
  permaDeletedAt?: Date;
}

export interface Hashtag {
  id: string;
  tag: string;
}

export interface AppSettings {
  themeColor: string;
  appTheme: AppThemeType;
  isVibrationEnabled: boolean;
  isSpeechToTextEnabled: boolean;
  language: Language;
  supportLanguage: string;
}

export interface UserViewSettings {
  id: string;
  uid: string;
  docVer?: number;
  docCollection?: string;
  encData?: EncryptionData;
  encryptionData?: EncryptionData;
  source?: string;
  appSettings: AppSettings;
  featureSettings: {
    hiddenCalendars: { [key: string]: Date };
    hiddenHabits: { [key: string]: Date };
    hiddenJournals: { [key: string]: Date };
    hiddenMoneytrackers: { [key: string]: Date };
    hideCompletedItems: boolean;
    showFeatureLabels: boolean;
    showUserGoal: boolean;
    userGoal: string | null;
    viewType: string;
    showTimebox: boolean;
    showCalendarView: boolean;
    showListFeature: boolean;
    showNoteFeature: boolean;
    showTodoFeature: boolean;
  };
  futureSettings: {
    futureCalendarEventSettings: {
      showTime: boolean;
      showReminder: boolean;
      showDuration: boolean;
      showRepeat: boolean;
      showCalendarName: boolean;
      viewSettingType: string;
      groupBy: string;
      collapsedView: boolean;
      showCounts: boolean;
    };
    futureHabitSettings: {
      descriptionType: string;
      showTime: boolean;
      showRepeat: boolean;
      showCalendarName: boolean;
      viewSettingType: string;
      groupBy: string;
      collapsedView: boolean;
      showCounts: boolean;
      showDate: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showHabitResponse: boolean;
      showImage: boolean;
      showInvalidEntries: boolean;
      showReminder: boolean;
      showTags: boolean;
    };
    futureJournalSettings: {
      descriptionType: string;
      showTime: boolean;
      collapsedView: boolean;
      groupBy: string;
      showCounts: boolean;
      showDate: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showImage: boolean;
      showInvalidEntries: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showMood: boolean;
      viewSettingType: string;
    };
    futureMoneyTrackerSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBySettings: string;
      showDate: boolean;
      showEmptyDays: boolean;
      showHashtag: boolean;
      showImage: boolean;
      showNetAmount: boolean;
      showSetupTitle: boolean;
      viewSettingType: string;
    };
    futureTodoSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBy: string;
      showChecklist: boolean;
      showCounts: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showImage: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
  };
  insightSettings: {
    insightHabitSettings: {
      resultInPercentage: boolean;
    };
    insightJournalSettings: {
      resultInPercentage: boolean;
    };
    insightTodoSettings: {
      resultInPercentage: boolean;
    };
  };
  listSettings: {
    myListSettings: {
      showDescription: boolean;
      itemCount: boolean;
      showCollaboratorsCount: boolean;
      showInvitedUsersCount: boolean;
      showAwaitingUserCount: boolean;
      showHashtags: boolean;
      groupByType: string;
      collapsedView: boolean;
      showCounts: boolean;
      viewSettingType: string;
    };
    myListItemSheetSettings: {
      showDescription: boolean;
      viewType: string;
      showLastUpdatedBy: boolean;
      showLastUpdatedAt: boolean;
    };
    sharedListSettings: {
      groupByType: string;
      collapsedView: boolean;
      showCounts: boolean;
      viewSettingType: string;
      showDescription: boolean;
      itemCount: boolean;
      showAccess: boolean;
      showHashtags: boolean;
    };
    sharedListItemSettings: {
      showDescription: boolean;
      viewType: string;
      showLastUpdatedBy: boolean;
      showLastUpdatedAt: boolean;
    };
    publicListSettings: {};
    publicListItemSheetSettings: {};
  };
  moneyTrackerSettings: {
    config: {
      title: string | null;
      currency: string;
      hasSetCurrency: boolean;
    };
    listViewSettings: {
      showDate: boolean;
      showHashtag: boolean;
      descriptionType: string;
      showImage: boolean;
      groupBySettings: string;
      showEmptyDays: boolean;
      collapsedView: boolean;
      showNetAmount: boolean;
      viewSettingType: string;
    };
  };
  noteSettings: {
    myNoteSettings: {
      noteDescriptionType: string;
      noteViewTime: boolean;
      noteViewDate: boolean;
      noteViewImage: boolean;
      noteViewMood: boolean;
      noteViewTags: boolean;
      notesGroupBy: string;
      viewType: string;
      showFilterRow: boolean;
      collapsedView: boolean;
      showMemberCount: boolean;
      showCounts: boolean;
    };
    savedNoteSettings: {
      noteDescriptionType: string;
      noteViewTime: boolean;
      noteViewDate: boolean;
      noteViewImage: boolean;
      noteViewMood: boolean;
      notesGroupBy: string;
      viewType: string;
      showFilterRow: boolean;
      collapsedView: boolean;
      showCounts: boolean;
    };
    sharedNoteSettings: {
      noteDescriptionType: string;
      noteViewTime: boolean;
      noteViewDate: boolean;
      noteViewImage: boolean;
      noteViewMood: boolean;
      notesGroupBy: string;
      viewType: string;
      showFilterRow: boolean;
      collapsedView: boolean;
      showCounts: boolean;
      showAccess: boolean;
    };
  };
  notificationSettings: {
    emailNotificationTime: CustomDate;
    emailNotificationTimezone: string;
    isDailyAgendaEmailNotificationEnabled: boolean;
    isDailyAgendaMobileNotificationEnabled: boolean;
    isDailyAgendaTmzDependent: boolean;
    isOverdueEmailNotificationEnabled: boolean;
    isMuteAllDeviceTmzDependent: boolean;
    mobileDailyAgendaNotificationTime: CustomDate;
    mobileSnoozeType: string;
    mobileSoundType: string;
    muteAllCustomPresetSelected: boolean;
    muteAllDevice: null;
    muteAllDeviceStartAt: Date | null;
    pinReminder: boolean;
    remindMeType: string;
  };
  pastSettings: {
    pastCalendarEventSettings: {
      collapsedView: boolean;
      groupBy: string;
      showCalendarName: boolean;
      showCounts: boolean;
      showDuration: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
    pastHabitSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBy: string;
      showCounts: boolean;
      showDate: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showHabitResponse: boolean;
      showImage: boolean;
      showInvalidEntries: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
    pastJournalSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBy: string;
      showCounts: boolean;
      showDate: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showImage: boolean;
      showInvalidEntries: boolean;
      showMood: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
    pastMoneyTrackerSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBySettings: string;
      showDate: boolean;
      showEmptyDays: boolean;
      showHashtag: boolean;
      showImage: boolean;
      showNetAmount: boolean;
      showSetupTitle: boolean;
      viewSettingType: string;
    };
    pastTodoSettings: {
      collapsedView: boolean;
      descriptionType: string;
      groupBy: string;
      showChecklist: boolean;
      showCounts: boolean;
      showDate: boolean;
      showDuration: boolean;
      showEmptyDays: boolean;
      showImage: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
  };
  todaySettings: {
    overdueSettings: {
      descriptionType: string;
      showChecklist: boolean;
      showDuration: boolean;
      showTime: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showImage: boolean;
      showTags: boolean;
      viewSettingType: string;
    };
    todayTabSettings: {
      descriptionType: string;
      showCalendarName: boolean;
      showChecklist: boolean;
      showDuration: boolean;
      showHabitResponse: boolean;
      showImage: boolean;
      showInvalidEntries: boolean;
      showMood: boolean;
      showReminder: boolean;
      showRepeat: boolean;
      showTags: boolean;
      showTime: boolean;
      viewSettingType: string;
    };
    unscheduleSettings: {
      descriptionType: string;
      showChecklist: boolean;
      showCompletedAt: boolean;
      showImage: boolean;
      showTags: boolean;
      viewSettingType: string;
    };
  };
  sessionId?: string;
  createdAt?: Date;
  cloudUpdatedAt?: Date | FieldValue | null;
  deletedAt?: Date | null;
  permaDeletedAt?: Date | null;
}