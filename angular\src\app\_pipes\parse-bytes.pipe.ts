import { Pipe, PipeTransform } from '@angular/core';
import { SettingsService } from '@app/_services/settings.service';

@Pipe({
    name: 'parseBytes',
    pure: true,
    standalone: true,
})

export class parseBytesPipe implements PipeTransform {

    constructor(public ss: SettingsService) { }

    transform(value: number | string | null | undefined, unitFrom: 'B' | 'KB' = 'B', useSI = false): string {
        if (value === null || value === undefined || value === '') {
            return this.ss.interpolateText('screen_account_mediaStorageStatusKBused', { storageUsed: '0' });
        }

        let kb = typeof value === 'string' ? parseFloat(value) : Number(value);
        if (!isFinite(kb) || isNaN(kb)) {
            return this.ss.interpolateText('screen_account_mediaStorageStatusKBused', { storageUsed: '0' });
        }

        let unit = 'KB';
        let val = kb;

        if (val >= 1024) {
            val /= 1024;
            unit = 'MB';
        }
        if (val >= 1024) {
            val /= 1024;
            unit = 'GB';
        }

        const final = Math.round(val).toString();

        switch (unit) {
            case 'KB':
                return this.ss.interpolateText('screen_account_mediaStorageStatusKBused', { storageUsed: final });
            case 'MB':
                return this.ss.interpolateText('screen_account_mediaStorageStatusMBused', { storageUsed: final });
            case 'GB':
                return this.ss.interpolateText('screen_account_mediaStorageStatusGBused', { storageUsed: final });
            default:
                return this.ss.interpolateText('screen_account_mediaStorageStatusKBused', { storageUsed: '0' });
        }
    }
}