import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, Signal, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FeatureSetup } from '@app/_interfaces/feature.interface';
import { FeatureStore, CalendarIntegrationStore, HabitStore, JournalStore, MoneyTrackerStore } from '@app/_stores';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { FeatureListComponent } from '../feature-list/feature-list.component';
import { Subject } from 'rxjs';
import { JournalSetupFormComponent } from '../../journals/journal-setup-form/journal-setup-form.component';
import { MoneyTrackerSetupFormComponent } from '../../money-tracker/money-tracker-setup-form/money-tracker-setup-form.component';
import { CalendarAccountFormComponent } from '../../calendar-integration/calendar-account-form/calendar-account-form.component';
import { HabitSetupFormComponent } from '../../habits/habit-setup-form/habit-setup-form.component';
import { CacheService } from '@app/_services/cache.service';
import { AlertService } from '@app/_services/alert.service';
import { EntityNameType, FeatureNameType, FeatureStatus } from '@app/_types/generic.type';
import { MapService } from '@app/_services/map.service';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-feature',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    InputToggleComponent
  ],
  templateUrl: './feature.component.html',
  styleUrl: './feature.component.scss'
})

export class FeatureComponent implements OnInit {

  readonly featureStore = inject(FeatureStore);
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.ss.viewSignals;
  unSubscribe = new Subject<void>();
  featureStatus: Signal<Record<FeatureStatus, string>> = this.mapService.featureStatusMap;
  confirmConfig: { [key in EntityNameType]?: { title: string, message: string, buttonText: string, buttonColor: string } } = {
    todo: {
      title: this.cc.texts()['overlay_hideFeature_todoTitle'],
      message: this.cc.texts()['overlay_hideFeature_todoContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    note: {
      title: this.cc.texts()['overlay_hideFeature_noteTitle'],
      message: this.cc.texts()['overlay_hideFeature_noteContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    list: {
      title: this.cc.texts()['overlay_hideFeature_listTitle'],
      message: this.cc.texts()['overlay_hideFeature_listContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    habit: {
      title: this.cc.texts()['overlay_hideFeature_habitTitle'],
      message: this.cc.texts()['overlay_hideFeature_habitContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    journal: {
      title: this.cc.texts()['overlay_hideFeature_journalTitle'],
      message: this.cc.texts()['overlay_hideFeature_journalContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    moneyTracker: {
      title: this.cc.texts()['overlay_hideFeature_moneyTrackerTitle'],
      message: this.cc.texts()['overlay_hideFeature_moneyTrackerContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    },
    calendarIntegration: {
      title: this.cc.texts()['overlay_hideFeature_calendarTitle'],
      message: this.cc.texts()['overlay_hideFeature_calendarContent'],
      buttonText: this.cc.texts()['overlay_hideFeature_disable'],
      buttonColor: 'color-11'
    }
  }

  constructor(
    public dialog: MatDialog,
    public cc: CacheService,
    public ss: SettingsService,
    private alertService: AlertService,
    private mapService: MapService
  ) {

  }

  ngOnInit() {

  }

  async toggleChange(feature: FeatureSetup, value: boolean) {
    switch (feature.entityType) {
      case 'todo':
        this.viewSignals()['showTodoFeature'].set(value);
        this.ss.updateViewSettings();
        break;
      case 'note':
        this.viewSignals()['showNoteFeature'].set(value);
        this.ss.updateViewSettings();
        break;
      case 'list':
        this.viewSignals()['showListFeature'].set(value);
        this.ss.updateViewSettings();
        break;
      case 'boolean':
      case 'timer':
      case 'numeric':
      case 'single':
      case 'multiple':
        let hiddenHabits = this.viewSignals()['hiddenHabits']();
        let hiddenHabitMap = this.viewSignals()['hiddenHabitsMap']();
        if (value) {
          hiddenHabits = hiddenHabits.filter((id: string) => id !== feature.id);
          delete hiddenHabitMap[feature.id];
        } else {
          hiddenHabits.push(feature.id);
          hiddenHabitMap[feature.id] = new Date();
        }
        this.viewSignals()['hiddenHabits'].set(hiddenHabits);
        this.viewSignals()['hiddenHabitsMap'].set(hiddenHabitMap);
        this.ss.updateViewSettings();
        break;
      case 'journal':
        let hiddenJournals = this.viewSignals()['hiddenJournals']();
        let hiddenJournalMap = this.viewSignals()['hiddenJournalsMap']();
        if (value) {
          hiddenJournals = hiddenJournals.filter((id: string) => id !== feature.id);
          delete hiddenJournalMap[feature.id];
        } else {
          hiddenJournals.push(feature.id);
          hiddenJournalMap[feature.id] = new Date();
        }
        this.viewSignals()['hiddenJournals'].set(hiddenJournals);
        this.viewSignals()['hiddenJournalsMap'].set(hiddenJournalMap);
        this.ss.updateViewSettings();
        break;
      case 'moneyTracker':
        let hiddenMoneytrackers = this.viewSignals()['hiddenMoneytrackers']();
        let hiddenMoneytrackersMap = this.viewSignals()['hiddenMoneytrackersMap']();
        if (value) {
          hiddenMoneytrackers = hiddenMoneytrackers.filter((id: string) => id !== feature.id);
          delete hiddenMoneytrackersMap[feature.id];
        } else {
          hiddenMoneytrackers.push(feature.id);
          hiddenMoneytrackersMap[feature.id] = new Date();
        }
        this.viewSignals()['hiddenMoneytrackers'].set(hiddenMoneytrackers);
        this.viewSignals()['hiddenMoneytrackersMap'].set(hiddenMoneytrackersMap);
        this.ss.updateViewSettings();
        break;
      case 'google':
      case 'microsoft':
        let hiddenCalendars = this.viewSignals()['hiddenCalendars']();
        let hiddenCalendarMap = this.viewSignals()['hiddenCalendarsMap']();
        if (value) {
          hiddenCalendars = hiddenCalendars.filter((id: string) => id !== feature.id);
          delete hiddenCalendarMap[feature.id];
        } else {
          hiddenCalendars.push(feature.id);
          hiddenCalendarMap[feature.id] = new Date();
        }
        this.viewSignals()['hiddenCalendars'].set(hiddenCalendars);
        this.viewSignals()['hiddenCalendarsMap'].set(hiddenCalendarMap);
        this.ss.updateViewSettings();
        break;
      default:
        break;
    }
  }

  addFeatures() {
    const dialog = this.dialog.open(FeatureListComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
    })
  }

  openFeatureSetup(feature: FeatureSetup) {
    switch (feature.entityType) {
      case 'boolean':
      case 'timer':
      case 'numeric':
      case 'single':
      case 'multiple':
        const setupHabitDialog = this.dialog.open(HabitSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
            habitType: feature.entityType,
          },
        });
        break;
      case 'journal':
        const setupDialog = this.dialog.open(JournalSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;
      case 'moneyTracker':
        const setupMoneyDialog = this.dialog.open(MoneyTrackerSetupFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;

      case 'google':
      case 'microsoft':
        const setupCalendarDialog = this.dialog.open(CalendarAccountFormComponent, {
          width: '100%',
          maxWidth: '750px',
          maxHeight: '90vh',
          minHeight: '90vh',
          disableClose: true,
          data: {
            mode: 'edit',
            value: feature.entity,
          },
        });
        break;

      default:
        break;
    }

  }

}
