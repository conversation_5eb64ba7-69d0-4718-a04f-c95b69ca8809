import { Pipe, PipeTransform } from '@angular/core';
import { MapService } from '@app/_services/map.service';
import { SettingsService } from '@app/_services/settings.service';
import { DayCode } from '@app/_types/generic.type';

@Pipe({
    name: 'parseRule',
    standalone: true,
})

export class ParseRulePipe implements PipeTransform {

    constructor(private mapService: MapService, public ss: SettingsService) { }

    transform(rrule: string): string {
        if (!rrule) return this.ss.texts()['bottomSheet_selectRepeat_off'];
        if (!rrule.startsWith('RRULE:')) return this.ss.texts()['bottomSheet_selectRepeat_off'];

        const ruleParts = rrule.replace('RRULE:', '').split(';');
        const ruleMap: { [key: string]: string } = {};

        ruleParts.forEach(part => {
            const [key, value] = part.split('=');
            ruleMap[key] = value;
        });

        switch (ruleMap['FREQ']) {
            case 'DAILY':
                return this.ss.texts()['bottomSheet_selectRepeat_daily'];

            case 'WEEKLY':
                const selectedDays: DayCode[] = ruleMap['BYDAY'].split(',') as DayCode[];
                const weekDays = Object.keys(this.mapService.dayMap()) as DayCode[];
                const orderedWeeks = selectedDays.sort((a, b) => weekDays.indexOf(a) - weekDays.indexOf(b));
                if (selectedDays.length === 7) {
                    return this.ss.texts()['bottomSheet_selectRepeat_daily'];
                } else if (selectedDays.length === 5 && selectedDays.includes('MO') && selectedDays.includes('TU') && selectedDays.includes('WE') && selectedDays.includes('TH') && selectedDays.includes('FR')) {
                    return this.ss.texts()['screen_common_repeatMondayToFriday'];
                } else {
                    const wdays = orderedWeeks.map(day => this.mapService.dayMap()[day] || day).join(', ');
                    return this.ss.interpolateText('overlay_repeatSelect_weeklyMultipleDays', { days: wdays });
                }

            case 'MONTHLY':
                if (ruleMap['BYMONTHDAY']) {
                    const isLastDay = ruleMap['BYMONTHDAY'] === '-1';
                    const selectedMonths = ruleMap['BYMONTH']?.split(',') as string[];
                    if (isLastDay) {
                        return this.ss.texts()['screen_common_repeatCustom'];
                    } else {
                        const selectedDates = ruleMap['BYMONTHDAY'].split(',');
                        if (selectedDates.length === 31 && selectedMonths.length === 12) {
                            return this.ss.texts()['bottomSheet_selectRepeat_daily'];
                        } else if (selectedDates.length === 1 && selectedMonths.length === 1) {
                            const month = this.mapService.monthMap()[selectedMonths[0]];
                            const date = selectedDates[0];
                            return this.ss.interpolateText('overlay_repeatSelect_previewMonthlyOneMonthAndOneDay', { date: `${month} ${date}` });
                        } else {
                            return this.ss.texts()['screen_common_repeatCustom'];
                        }
                    }
                } else {
                    const selectedByDays = ruleMap['BYDAY'].split(',');

                    const weekNumbersSet = new Set<string>();
                    const weekDaysSet = new Set<string>();

                    selectedByDays.forEach(item => {
                        const match = item.match(/^(-?\d)?([A-Z]{2})$/);
                        if (match) {
                            const [, weekNum, dayCode] = match;
                            if (weekNum) weekNumbersSet.add(weekNum);
                            weekDaysSet.add(dayCode);
                        }
                    });

                    if (weekNumbersSet.size === 5 && weekDaysSet.size === 7) {
                        return this.ss.texts()['bottomSheet_selectRepeat_daily'];
                    } else {
                        return this.ss.texts()['screen_common_repeatCustom'];
                    }
                };

            case 'YEARLY':
                const month = this.mapService.monthMap()[ruleMap['BYMONTH']];
                const date = ruleMap['BYMONTHDAY'];
                return this.ss.interpolateText('overlay_repeatSelect_previewMonthlyOneMonthAndOneDay', { date: `${month} ${date}` });

            default:
                return this.ss.texts()['bottomSheet_selectRepeat_off'];
        }
    }
}