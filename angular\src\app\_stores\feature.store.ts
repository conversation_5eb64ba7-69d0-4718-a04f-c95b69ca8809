import { signalStore, withComputed, withState, withMethods, patchState } from "@ngrx/signals";
import { computed, inject, signal, WritableSignal } from "@angular/core";
import { DependencyService } from "@app/_services/dependency.service";
import { JournalStore } from "./journal.store";
import { CalendarEntitySetup, CalendarEventEntity, EntitySetup, EntityWithDateGroup, FeatureSetup } from "@app/_interfaces/feature.interface";
import { UtilsService } from "@app/_services/utils.service";
import { TodoStore } from "./todo.store";
import { getDateString } from "@app/_utils/utils";
import { MoneyTrackerStore } from "./money-tracker.store";
import { CalendarIntegrationStore } from "./calendar-integration.store";
import { HabitStore } from "./habit.store";
import { EntityNameType, FeatureStatus } from "@app/_types/generic.type";
import { ListStore } from "./list.store";
import { NoteStore } from "./note.store";
import { MapService } from "@app/_services/map.service";
import { CacheService } from "@app/_services/cache.service";
import { DataService } from "@app/_services/data.service";
import { StorageService } from "@app/_services/storage.servive";
import { SettingsService } from "@app/_services/settings.service";

type FeatureState = {
    features: FeatureSetup[];
    activeFeatures: FeatureSetup[];
    isLoading: boolean;
    firstEntityDateString: string | null;
    filter: { dateString: string, hideCompleted: WritableSignal<boolean>, status: WritableSignal<FeatureStatus> };
};

const initialState: FeatureState = {
    features: [],
    activeFeatures: [],
    isLoading: false,
    firstEntityDateString: null,
    filter: { dateString: getDateString(), hideCompleted: signal<boolean>(true), status: signal<FeatureStatus>('active') }
};

export const FeatureStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((
        store,
        ss = inject(SettingsService),
        listStore = inject(ListStore),
        noteStore = inject(NoteStore),
        todoStore = inject(TodoStore),
        journalStore = inject(JournalStore),
        habitStore = inject(HabitStore),
        moneyTrackerStore = inject(MoneyTrackerStore),
        calendarStore = inject(CalendarIntegrationStore),
        ds = inject(DependencyService),
        cc = inject(CacheService),
        dataService = inject(DataService)
    ) => ({

        storeMap: computed<{ [key in EntityNameType]: any }>(() => {
            return {
                todo: todoStore,
                habit: habitStore,
                journal: journalStore,
                moneyTracker: moneyTrackerStore,
                calendarIntegration: calendarStore,
                list: listStore,
                note: noteStore
            };
        }),

        premiumFeatures: computed<FeatureSetup[]>(() => {
            return dataService.premiumFeatures();
        }),

        features: computed<FeatureSetup[]>(() => {
            const settings = ss.viewSettings();
            const status = store.filter().status();
            const defaultOnes = dataService.defaultFeatures().map(f => {
                if (f.entityType === 'todo') {
                    f.disabled = settings.featureSettings.showTodoFeature;
                } else if (f.entityType === 'note') {
                    f.disabled = settings.featureSettings.showNoteFeature;
                } else if (f.entityType === 'list') {
                    f.disabled = settings.featureSettings.showListFeature;
                }
                return f;
            });
            const features: FeatureSetup[] = [];
            const habitSetups = habitStore.activeSetups();
            const journalSetups = journalStore.activeSetups();
            const moneyTrackerSetups = moneyTrackerStore.activeSetups();
            const calendarintegrations = calendarStore.activeAccounts();

            habitSetups.forEach(habitSetup => {
                const habitFeature = habitStore.getHabitFeature(habitSetup)
                if (habitFeature.status === status) {
                    features.push(habitFeature);
                }
            });
            journalSetups.forEach(journalSetup => {
                const journalFeature = journalStore.getJournalFeature(journalSetup);
                if (journalFeature.status === status) {
                    features.push(journalFeature);
                }
            });
            moneyTrackerSetups.forEach(moneyTrackerSetup => {
                const moneyTrackerFeature = moneyTrackerStore.getMoneyTrackerFeature(moneyTrackerSetup);
                if (moneyTrackerFeature.status === status) {
                    features.push(moneyTrackerFeature);
                }
            });
            calendarintegrations.forEach(calendarintegration => {
                const calendarFeature = calendarStore.getCalendarFeature(calendarintegration);
                if (calendarFeature.status === status) {
                    features.push(calendarFeature);
                }
            });
            const sortedFeatures = features.sort((a, b) => {
                if (a.createdAt && b.createdAt) {
                    return a.createdAt > b.createdAt ? -1 : 1;
                } else {
                    return 1;
                }
            });

            return [...(status === 'active' ? defaultOnes : []), ...sortedFeatures];
        }),
        calendarEventEntities: computed<CalendarEventEntity[]>(() => {
            const entities: CalendarEventEntity[] = [];
            entities.push(...todoStore.calendarEventEntities());
            entities.push(...journalStore.calendarEventEntities());
            return entities;
        }),
        entities: computed<{ withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], completedEntities: EntitySetup[] }>(() => {
            const date = store.filter.dateString();
            const hideCompleted = ss.viewSettings().featureSettings.hideCompletedItems;
            const computedHabits = habitStore.getComputedEntities(date);
            const computedJournals = journalStore.getComputedEntities(date);
            const computedTodos = ss.viewSignals()['showTodoFeature']() ? todoStore.getComputedEntities(date) : { withTime: [], withoutTime: [], calenderEvents: [] };
            const computedMoneyTracker = moneyTrackerStore.getComputedEntities(date);
            const computedCalendarEvents = calendarStore.getComputedEntities(date);

            const allStatusEntitiesWithTime = [...computedTodos.withTime, ...computedHabits.withTime, ...computedJournals.withTime];

            const tempHideCompleted = store.filter().hideCompleted();
            const finalHideCompleted = hideCompleted ? tempHideCompleted : false;

            let filteredEntitiesWithTime = allStatusEntitiesWithTime;
            let completedEntitiesWithTime = [];
            let nonCompletedEntitiesWithTime = [];
            let completedEntities: EntitySetup[] = [];

            if (hideCompleted) {
                completedEntitiesWithTime = allStatusEntitiesWithTime.filter(item => item.status === 'completed');
                nonCompletedEntitiesWithTime = allStatusEntitiesWithTime.filter(item => item.status !== 'completed');
                filteredEntitiesWithTime = [...nonCompletedEntitiesWithTime];
                completedEntities = [...completedEntitiesWithTime];
            }

            const allStatusEntitiesWithoutTime = [...computedTodos.withoutTime, ...computedHabits.withoutTime, ...computedJournals.withoutTime];
            let filteredEntitiesWithoutTime = allStatusEntitiesWithoutTime;
            let completedEntitiesWithoutTime = [];
            let nonCompletedEntitiesWithoutTime = [];

            if (hideCompleted) {
                completedEntitiesWithoutTime = allStatusEntitiesWithoutTime.filter(item => item.status === 'completed');
                nonCompletedEntitiesWithoutTime = allStatusEntitiesWithoutTime.filter(item => item.status !== 'completed');
                filteredEntitiesWithoutTime = [...nonCompletedEntitiesWithoutTime];
                completedEntities = [...completedEntities, ...completedEntitiesWithoutTime];
            }

            const entityList = {
                withTime: [...(finalHideCompleted ? filteredEntitiesWithTime : allStatusEntitiesWithTime), ...computedCalendarEvents.withTime].sort((a, b) => {
                    const [aHours, aMinutes] = a.startAt?.timeString.split(":").map(Number) || [0, 0];
                    const [bHours, bMinutes] = b.startAt?.timeString.split(":").map(Number) || [0, 0];

                    return aHours * 60 + aMinutes - (bHours * 60 + bMinutes);
                }),
                withoutTime: [...(finalHideCompleted ? filteredEntitiesWithoutTime : allStatusEntitiesWithoutTime), ...computedMoneyTracker.withoutTime],
                calenderEvents: [...computedTodos.calenderEvents, ...computedHabits.calenderEvents, ...computedJournals.calenderEvents, ...computedCalendarEvents.calenderEvents],
                completedEntities: completedEntities
            };
            return entityList;
        }),
        activeEntities: computed<EntityNameType[]>(() => {
            const entities: EntityNameType[] = [];
            const isTodoEnabled = ss.viewSignals()['showTodoFeature']();
            const isNoteEnabled = ss.viewSignals()['showNoteFeature']();
            const isListEnabled = ss.viewSignals()['showListFeature']();
            const isHabitEnabled = habitStore.enabledSetups().length > 0;
            const isJournalEnabled = journalStore.enabledSetups().length > 0;
            const isMoneyTrackerEnabled = moneyTrackerStore.enabledSetups().length > 0;
            const isCalendarIntegrationEnabled = calendarStore.enabledAccounts().length > 0;
            if (isTodoEnabled) {
                entities.push('todo');
            }
            if (isListEnabled) {
                entities.push('list');
            }
            if (isNoteEnabled) {
                entities.push('note');
            }
            if (isHabitEnabled) {
                entities.push('habit');
            }
            if (isJournalEnabled) {
                entities.push('journal');
            }
            if (isMoneyTrackerEnabled) {
                entities.push('moneyTracker');
            }
            if (isCalendarIntegrationEnabled) {
                entities.push('calendarIntegration');
            }
            return entities;
        }),
        completedEntities: computed<EntitySetup[]>(() => {
            const date = store.filter.dateString();
            const entityList: EntitySetup[] = [];
            // const completedTodos = todoStore.getCompletedTodos(date).entities;
            // const entityList = [...completedJournals, ...completedTodos]
            return entityList;
        }),
        overdueTodos: computed<{ data: EntityWithDateGroup[], count: number }>(() => {
            const firstDateString = todoStore.firstDateString();
            const dateStrings = cc.getPastDates(new Date(firstDateString));
            const groupedData = new Map<string, EntityWithDateGroup>();
            let count = 0;

            dateStrings.forEach(dateString => {
                const entities = todoStore.getComputedEntities(dateString, false, 'date');
                let allEntities = [...entities.withoutTime, ...entities.withTime].filter(entity => entity.status === 'missed');
                count += allEntities.length;
                if (!groupedData.has(dateString)) {
                    groupedData.set(dateString, {
                        id: dateString,
                        name: dateString,
                        data: [],
                        dateMap: {}
                    });
                }
                groupedData.get(dateString)!.data.push(...allEntities);
                if (!groupedData.get(dateString)!.dateMap[dateString]) {
                    groupedData.get(dateString)!.dateMap[dateString] = [];
                }
                groupedData.get(dateString)!.dateMap[dateString].push(...allEntities);
            });
            const orderedKeys = [...groupedData.keys()];
            const result = orderedKeys
                .filter(key => key !== '' && (groupedData.get(key)!.data.length !== 0 || false))                     // all non-empty keys, in original order
                .map(key => groupedData.get(key)!)
                .concat(groupedData.has('') ? [groupedData.get('')!] : []);

            return { data: result, count };
        })
    })),
    withMethods((
        store,
        utilsService = inject(UtilsService),
        mapService = inject(MapService)
    ) => ({

        filterDate: (dateString: string) => {
            patchState(store, (state) => ({ filter: { ...state.filter, dateString } }));
        },

        groupByFeature: () => {
            const allEntities = [...store.entities().withTime, ...store.entities().withoutTime];

            const groupedMap = allEntities.reduce(
                (acc, entity) => {
                    const groupedMap = acc;
                    const id = entity.entityName;

                    if (!groupedMap.has(id)) {
                        groupedMap.set(id, {
                            id,
                            name: mapService.entityToGroupNameMap()[id],
                            data: []
                        });
                    }
                    groupedMap.get(id)!.data.push(entity);
                    return acc;
                },
                new Map<string, { id: string; name: string; data: EntitySetup[] }>()
            );

            const groupedData = [...groupedMap.values()];

            return groupedData;
        },

        findClosestTime(currentTime: string = '') {
            const timeStrings = store.entities().withTime.map(entity => entity.startAt?.timeString);

            if (timeStrings.length === 0) {
                return "00:00";
            }

            // Get current time if not provided
            if (!currentTime) {
                const now = new Date();
                const hours = now.getHours().toString().padStart(2, '0');
                const minutes = now.getMinutes().toString().padStart(2, '0');
                currentTime = `${hours}:${minutes}`;
            }

            // Convert time string to minutes for easier comparison
            function timeToMinutes(timeStr: string) {
                const [hours, minutes] = timeStr.split(':').map(Number);
                return hours * 60 + minutes;
            }

            const currentMinutes = timeToMinutes(currentTime);

            // Convert all time strings to minutes and sort them
            const timeData = timeStrings
                .map(timeStr => ({
                    original: timeStr,
                    minutes: timeToMinutes(timeStr || '00:00')
                }))
                .sort((a, b) => a.minutes - b.minutes);

            // If current time is less than all time strings, return "00:00"
            if (currentMinutes < timeData[0].minutes) {
                return "00:00";
            }

            // If current time is greater than all time strings, return the greatest
            if (currentMinutes >= timeData[timeData.length - 1].minutes) {
                return timeData[timeData.length - 1].original;
            }

            // Find the closest previous time string
            let closestPrevious = "00:00";

            for (let i = 0; i < timeData.length; i++) {
                if (timeData[i].minutes <= currentMinutes) {
                    closestPrevious = timeData[i].original || '00:00';
                } else {
                    break;
                }
            }

            return closestPrevious;
        }
    })),
);
