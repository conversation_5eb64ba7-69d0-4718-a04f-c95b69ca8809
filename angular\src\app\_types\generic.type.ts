import { Habit, HabitSetup } from "@app/_interfaces/habit.interface";
import { Journal, JournalSetup } from "@app/_interfaces/journal.interface";
import { List } from "@app/_interfaces/list.interface";
import { MoneyTrackerSetup, MoneyTransaction } from "@app/_interfaces/money-tracker.interface";
import { Note } from "@app/_interfaces/note.interface";
import { Todo } from "@app/_interfaces/todo.interface";

export type Entity = List | Note | Todo | Habit | HabitSetup | Journal | JournalSetup | MoneyTrackerSetup | MoneyTransaction;
export type EntityName = 'list' | 'note' | 'todo' | 'habit' | 'journal' | 'moneyTracker' | 'calendarIntegration';

export type LanguageCode = 'en' | 'de' | 'fr' | 'es' | 'it' | 'pt';
export type Language = 'english' | 'french' | 'german' | 'italian' | 'portuguese' | 'spanish';
export type ThemeMode = 'dark' | 'light';
export type AppThemeType = ThemeMode | 'systemDefault';

export type AttachmentType = 'image' | 'audio' | 'video' | 'document' | 'txt';
export type EntityStatusType = 'completed' | 'hasvalue' | 'canceled' | '';
export type EntityNameType = 'list' | 'note' | 'todo' | 'journal' | 'habit' | 'moneyTracker' | 'calendarIntegration';
export type EntityStatus = 'all' | 'missed' | 'overdue' | 'draft' | 'skipped' | 'incomplete' | 'completed' | 'none';
export type CalendarIntegrationType = 'google' | 'microsoft';
export type SyncType = 'syncNotifications' | 'onlySync' | 'disabled';
export type HabitType = 'boolean' | 'numeric' | 'timer' | 'single' | 'multiple';
export type FeatureNameType = 'todo' | 'note' | 'list' | 'journal' | 'moneyTracker' | CalendarIntegrationType | HabitType;
export type TimerStopType = 'onRoundEnd' | 'onGoalReached' | 'never';
export type AlertToneType = 'mevolve_1' | 'mevolve_2' | 'mevolve_3' | 'mevolve_4' | 'mevolve_5' | 'none';

export type ShowType = 'habitResponse' | 'setup' | 'calendarName' | 'completedAt' | 'mood' | 'date' | 'time' | 'reminder' | 'duration' | 'repeat' | 'checklist' | 'attachments' | 'label' | 'hashtag' | 'invalidEntry' | 'emptyDays' | 'description' | 'collaboratorsCount' | 'invitedUsersCount' | 'awaitingUserCount' | 'itemCount' | 'lastUpdatedAt' | 'lastUpdatedBy' | 'access';

export type ListItemAddOn = 'checkbox' | 'customText' | 'none';

export type FeatureStatus = 'active' | 'upcoming' | 'completed';

export type EndDateRepeatMode = 'until' | 'occurrence';

export type RepeatType = 'OFF' | 'DAILY' | 'WEEK_WORKING_DAY' | 'WEEKLY' | 'MONTHLY' | 'MONTHLY_NTH_DAY' | 'MONTHLY_LAST_DAY' | 'YEARLY';

export type DayCode = 'MO' | 'TU' | 'WE' | 'TH' | 'FR' | 'SA' | 'SU';

export type RemindType = 'BEFORE' | 'ON_TIME' | 'AFTER';

export type CollaboraterRole = 'owner' | 'editor' | 'viewer';
export type CollaboraterStatus = 'left' | 'blocked';

export type ShowTypeKey = 'listShowType' | 'listItemShowType' | 'sharedListShowType' | 'sharedListItemShowType' | 'noteShowType' | 'sharedNoteShowType' | 'savedNoteShowType' | 'entityShowType' | 'overdueShowType' | 'unscheduleShowType' | 'pastTodoShowType' | 'futureTodoShowType' | 'pastHabitShowType' | 'futureHabitShowType' | 'pastJournalShowType' | 'futureJournalShowType' | 'pastMoneyTrackerShowType' | 'futureMoneyTrackerShowType' | 'pastCalendarEventShowType' | 'futureCalendarEventShowType';

export type TransactionType = 'income' | 'expense';

export type RepeatFilterType = 'all' | 'one_time' | 'repeated';

export type UploadStatus = 'local' | 'cloud' | 'temporary';

export type UpdateType = 'raw' | 'patch';

export type subscriptionPlan = 'free' | 'basic' | 'pro' | 'plus';

export type subscriptionState = 'none' | 'subscribed' | 'subscriptionExpired';

export type subscriptionType = 'monthly' | 'yearly' | 'custom';
