import { CommonModule } from '@angular/common';
import { Component, Inject, inject, ViewChild } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Hashtag } from '@app/_interfaces/user.interface';
import { AlertService } from '@app/_services/alert.service';
import { UtilsService } from '@app/_services/utils.service';
import { Subject, takeUntil } from 'rxjs';
import * as _ from "lodash";
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { CacheService } from '@app/_services/cache.service';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-hashtag-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SvgComponent,
    InputTextComponent,
  ],
  templateUrl: './hashtag-form.component.html',
  styleUrl: './hashtag-form.component.scss'
})

export class HashtagFormComponent {

  @ViewChild('hTagForm') hTagForm!: NgForm;
  unSubscribe = new Subject<void>();
  hashtagForm: FormGroup;
  hashtagInitial: Hashtag;
  mode: 'new' | 'edit';

  constructor(
    public dialogRef: MatDialogRef<HashtagFormComponent>,
    private alertService: AlertService,
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', hashtag: Hashtag },
    private utilsService: UtilsService,
    public cc: CacheService,
    public ss: SettingsService
  ) {

    this.mode = data.mode;

    this.hashtagInitial = data.mode === 'new' ? this.initiateForm() : this.initiateForm(data.hashtag);
    console.log("i am called data-->>, data", data.hashtag)

    this.hashtagForm = this.fb.group({
      id: new FormControl(this.hashtagInitial.id, Validators.required),
      tag: new FormControl(this.hashtagInitial.tag, [Validators.required, Validators.maxLength(20), notOnlyWhitespace()])
    });

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  getFc(fcName: string): FormControl {
    return this.hashtagForm.get(fcName) as FormControl;
  }

  initiateForm(hashtag?: Hashtag): Hashtag {
    return {
      id: hashtag ? hashtag.id : this.utilsService.getNumId(),
      tag: hashtag ? hashtag.tag : '',
    };
  }

  hasChanges() {
    const initial = _.cloneDeep(this.hashtagInitial);
    const current = _.cloneDeep(this.hashtagForm.value);
    return !_.isEqual(initial, current);
  }

  async save() {
    if (this.mode === 'new') {
      this.ss.addHashtag(this.hashtagForm.value);
    } else {
      this.ss.updateHashtag(this.hashtagForm.value);
    }
    this.dialogRef.close();
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
      this.dialogRef.close();
    } else {
      this.dialogRef.close();
    }
  }

  reset() {
    this.hashtagForm.reset();
    this.hTagForm.resetForm();
    this.hashtagForm.patchValue(this.hashtagInitial);
  }
}
