import { Component, computed, inject, Inject, signal, Signal, ViewChild, WritableSignal } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormControl, FormGroup, FormsModule, NgForm, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { HabitSetup } from '@app/_interfaces/habit.interface';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { UtilsService } from '@app/_services/utils.service';
import { HabitStore } from '@app/_stores';
import { getCustomDate, getNumId } from '@app/_utils/utils';
import { CollaboratorComponent } from '@app/components/addons/collaborators/collaborator/collaborator.component';
import { HashtagComponent } from '@app/components/addons/hashtags/hashtag/hashtag.component';
import { Subject, takeUntil } from 'rxjs';
import * as _ from 'lodash';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { InputDatePeriodComponent } from '@app/components/shared/inputs/input-date-period/input-date-period.component';
import { InputGoalComponent } from '@app/components/shared/inputs/input-goal/input-goal.component';
import { InputHashtagComponent } from '@app/components/shared/inputs/input-hashtag/input-hashtag.component';
import { InputReminderComponent } from '@app/components/shared/inputs/input-reminder/input-reminder.component';
import { InputSelectComponent } from '@app/components/shared/inputs/input-select/input-select.component';
import { InputTextComponent } from '@app/components/shared/inputs/input-text/input-text.component';
import { InputTimeComponent } from '@app/components/shared/inputs/input-time/input-time.component';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { CollaboraterRole, CollaboraterStatus, HabitType } from '@app/_types/generic.type';
import { InputNumberComponent } from '@app/components/shared/inputs/input-number/input-number.component';
import { InputTimerComponent } from '@app/components/shared/inputs/input-timer/input-timer.component';
import { InputTimerStopTypeComponent } from '@app/components/shared/inputs/input-timer-stop-type/input-timer-stop-type.component';
import { InputAlertToneComponent } from '@app/components/shared/inputs/input-alert-tone/input-alert-tone.component';
import { InputOptionsComponent } from '@app/components/shared/inputs/input-options/input-options.component';
import { notOnlyWhitespace } from '@app/_directives/form-validator.directive';
import { DependencyService } from '@app/_services/dependency.service';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-habit-setup-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatMenuModule,
    InputTextComponent,
    InputHashtagComponent,
    InputDatePeriodComponent,
    SvgComponent,
    InputTimeComponent,
    InputReminderComponent,
    InputGoalComponent,
    InputSelectComponent,
    InputToggleComponent,
    InputNumberComponent,
    InputTimerComponent,
    InputTimerStopTypeComponent,
    InputAlertToneComponent,
    InputOptionsComponent
  ],
  templateUrl: './habit-setup-form.component.html',
  styleUrl: './habit-setup-form.component.scss'
})

export class HabitSetupFormComponent {

  @ViewChild('hSetupForm') hSetupForm!: NgForm;
  unSubscribe = new Subject<void>();
  habitSetupForm: FormGroup;
  habitSetupInitial: HabitSetup;
  readonly habitStore = inject(HabitStore);
  submitted: boolean = false;

  mode: WritableSignal<'new' | 'edit'> = signal('new');

  setup: Signal<HabitSetup> = computed(() => {
    return this.habitStore.idToSetup()[this.data.value?.id || ''] || {} as HabitSetup;
  });

  isCollab: Signal<boolean> = computed(() => {
    return (this.setup().members?.memberHashedEmails?.length || 0) > 0;
  });

  role: Signal<CollaboraterRole> = computed(() => {
    if (this.mode() === 'new') {
      return 'owner';
    } else if (this.setup().uid === this.cc.user.uid) {
      return 'owner';
    } else if (this.setup().members) {
      return this.setup().members?.membersConfig[this.cc.user.hashedEmail]?.role;
    } else {
      return 'viewer';
    }
  });

  constructor(
    public dialogRef: MatDialogRef<HabitSetupFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { mode: 'new' | 'edit', value: HabitSetup, habitType: HabitType },
    private fb: FormBuilder,
    public cc: CacheService,
    private alertService: AlertService,
    private utilsService: UtilsService,
    public dialog: MatDialog,
    public ds: DependencyService,
    public ss: SettingsService
  ) {
    this.mode.set(data.mode);
    this.habitSetupInitial = data.mode == 'new' ? this.initiateForm() : this.initiateForm(data.value);
    console.log("habitSetupInitial--->", this.habitSetupInitial);
    console.log("habitType--->", this.data.value);

    this.habitSetupForm = this.fb.group({
      id: new FormControl(this.habitSetupInitial.id, Validators.required),
      title: new FormControl(this.habitSetupInitial.title, [Validators.required, Validators.maxLength(120), notOnlyWhitespace()]),
      habitType: new FormControl(this.habitSetupInitial.habitType),
      numericGoal: new FormControl(this.habitSetupInitial.numericGoal, Validators.maxLength(10)),
      numericUnit: new FormControl(this.habitSetupInitial.numericUnit, this.data.habitType === 'numeric' ? [Validators.required, Validators.maxLength(20), notOnlyWhitespace()] : null),
      timerGoal: new FormControl(this.habitSetupInitial.timerGoal),
      timerStopType: new FormControl(this.habitSetupInitial.timerStopType),
      durationRepeatType: new FormControl(this.habitSetupInitial.durationRepeatType),
      durationRepeatCount: new FormControl(this.habitSetupInitial.durationRepeatCount),
      habitOptions: this.fb.array([]),
      isVibrationEnabled: new FormControl(this.habitSetupInitial.isVibrationEnabled),
      alarmSoundType: new FormControl(this.habitSetupInitial.alarmSoundType),
      alarmSoundVolume: new FormControl(this.habitSetupInitial.alarmSoundVolume),
      tags: new FormControl(this.habitSetupInitial.tags),
      startAt: new FormControl(this.habitSetupInitial.startAt),
      endAt: new FormControl(this.habitSetupInitial.endAt),
      isStartTimeSet: new FormControl(this.habitSetupInitial.isStartTimeSet),
      isTmzAffected: new FormControl(this.habitSetupInitial.isTmzAffected),
      duration: new FormControl(this.habitSetupInitial.duration),
      repeat: new FormControl(this.habitSetupInitial.repeat),
      reminderAt: new FormControl(this.habitSetupInitial.reminderAt),
      uid: new FormControl(this.habitSetupInitial.uid, Validators.required)
    });

    if (this.habitSetupInitial.habitOptions && this.habitSetupInitial.habitOptions.length !== 0) {
      this.habitSetupInitial.habitOptions.forEach(option => {
        this.habitOptions.push(this.addOptionForm(option));
      });
    }

    dialogRef.backdropClick().pipe(takeUntil(this.unSubscribe)).subscribe(async () => {
      this.closeDialog();
    });
  }

  initiateForm(setup?: HabitSetup): HabitSetup {
    return {
      id: setup ? setup.id : this.utilsService.getNewId(),
      title: setup ? setup.title : '',
      habitType: setup ? setup.habitType : this.data.habitType,
      numericGoal: setup ? setup.numericGoal : null,
      numericUnit: setup ? setup.numericUnit : null,
      timerGoal: setup ? setup.timerGoal : null,
      timerStopType: setup ? setup.timerStopType : 'onRoundEnd',
      durationRepeatType: setup ? setup.durationRepeatType : 0,
      durationRepeatCount: setup ? setup.durationRepeatCount : 1,
      habitOptions: setup ? setup.habitOptions : [],
      isVibrationEnabled: setup ? setup.isVibrationEnabled : true,
      alarmSoundType: setup ? setup.alarmSoundType : 'mevolve_1',
      alarmSoundVolume: setup ? setup.alarmSoundVolume : 100,
      tags: setup ? setup.tags : [],
      startAt: setup ? setup.startAt : getCustomDate(),
      endAt: setup ? setup.endAt : null,
      isStartTimeSet: setup ? setup.isStartTimeSet : false,
      isTmzAffected: setup ? setup.isTmzAffected : false,
      duration: setup ? setup.duration : 15,
      repeat: setup ? setup.repeat : ["RRULE:FREQ=DAILY"],
      reminderAt: setup ? setup.reminderAt : [],
      uid: setup ? setup.uid : this.cc.user.uid,
    }
  }

  addOptionForm(option?: { id: number; value: string }) {
    return this.fb.group({
      id: new FormControl(option ? option.id : getNumId(), Validators.required),
      value: new FormControl(option ? option.value : '', Validators.required),
    });
  }

  get habitOptions(): FormArray {
    return this.habitSetupForm.get('habitOptions') as FormArray;
  }

  getFc(fcName: string): FormControl {
    return this.habitSetupForm.get(fcName) as FormControl;
  }

  hasChanges() {
    const initial = _.cloneDeep(this.habitSetupInitial);
    const current = _.cloneDeep(this.habitSetupForm.value);
    return !_.isEqual(initial, current);
  }

  addCollaborator() {
    const confirmDialog = this.dialog.open(CollaboratorComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
      data: {
        collection: 'habitSetups',
        entityData: this.data.value,
        isPublic: false,
      },
    });
    return confirmDialog.afterClosed();
  }

  async removeCollab(action: CollaboraterStatus) {
    const res = await this.alertService.confirm(this.cc.texts()[action === 'left' ? 'overlay_leaveHabit_title' : 'overlay_blockHabit_title'], this.cc.texts()[action === 'left' ? 'overlay_leaveHabit_content' : 'overlay_blockHabit_content'], this.cc.texts()[action === 'left' ? 'screen_common_sharingLeave' : 'screen_common_sharingBlock'], this.cc.texts()['screen_common_buttonCancel'], 'color-11', 'color-7');
    if (!res) return;
    if (res.confirm === true) {
      await this.ss.leaveCollaboratedEntity(action, this.setup().id, 'habitSetups');
      this.dialogRef.close();
    } else {
      return;
    }
  }

  openHashtagsDialog() {
    const dialog = this.dialog.open(HashtagComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        values: this.habitSetupForm.value.tags,
        type: 'map'
      },
    })

    dialog.afterClosed().pipe(takeUntil(this.unSubscribe)).subscribe(result => {
      if (result) {
        this.habitSetupForm.get('tags')?.setValue(result);
      }
    });
  }

  async closeDialog() {
    if (this.hasChanges()) {
      const res = await this.alertService.confirm(this.cc.texts()['overlay_saveOrDiscardConfirmation_title'], this.cc.texts()['screen_common_discardContent'], this.cc.texts()['screen_common_save'], this.cc.texts()['screen_common_discard'], 'color-35', 'color-11');
      if (!res) return;
      if (res.confirm === true) {
        this.save();
      } else if (res.confirm === false) {
        this.dialogRef.close();
      }
    } else {
      this.dialogRef.close();
    }
  }

  save() {
    this.submitted = true;
    switch (this.data.habitType) {
      case 'boolean':
      case 'timer':
        if (this.habitSetupForm.invalid) {
          this.alertService.alert(this.cc.texts()['overlay_habitSetupAddMandatoryAlert_title'], this.cc.texts()['overlay_habitSetupAddMandatoryAlert_yesOrNoTitle'], this.cc.texts()['screen_common_ok']);
          return;
        }
        break;
      case 'numeric':
        if (this.habitSetupForm.invalid) {
          this.alertService.alert(this.cc.texts()['overlay_habitSetupAddMandatoryAlert_title'], this.cc.texts()['overlay_habitSetupAddMandatoryAlert_numericalValueTitle'], this.cc.texts()['screen_common_ok']);
          return;
        }
        break;
      case 'single':
      case 'multiple':
        if (this.habitSetupForm.invalid || this.habitOptions.length < 2) {
          this.alertService.alert(this.cc.texts()['overlay_habitSetupAddMandatoryAlert_title'], this.cc.texts()['overlay_habitSetupAddMandatoryAlert_multipleChoiceTitle'], this.cc.texts()['screen_common_ok']);
          return;
        }
        break;
      default:
        break;
    }
    const setup: HabitSetup = this.data.mode === 'new' ? this.habitStore.getNewSetup() : this.data.value;
    const updatedSetup = { ...setup, ...this.habitSetupForm.value };

    if (this.data.mode === 'new') {
      this.habitStore.addSetup(updatedSetup);
    } else if (this.data.mode === 'edit') {
      this.habitStore.updateSetups([updatedSetup]);
    }
    this.dialogRef.close();
  }

  deleteSetup() {
    this.habitStore.deleteSetups([this.data.value]);
    this.dialogRef.close();
  }

}
