import { CommonModule } from '@angular/common';
import { Component, computed, inject, Signal, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { ParseUpdatedResponsePipe } from '@app/_pipes/parse-update-response.pipe';
import { AlertService } from '@app/_services/alert.service';
import { CacheService } from '@app/_services/cache.service';
import { SettingsService } from '@app/_services/settings.service';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { AppThemeType } from '@app/_types/generic.type';
import { PinDialogComponent } from '@app/components/shared/dialogs/pin-dialog/pin-dialog.component';
import { ThemeDialogComponent } from '@app/components/shared/dialogs/theme-dialog/theme-dialog.component';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { SvgComponent } from '@app/components/shared/svg/svg.component';

@Component({
  selector: 'app-app-settings',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    ParseUpdatedResponsePipe,
    InputToggleComponent,
    InputDropdownComponent
  ],
  templateUrl: './app-settings.component.html',
  styleUrl: './app-settings.component.scss'
})

export class AppSettingsComponent {

  metaSignals: Signal<Record<string, WritableSignal<any>>> = computed(() => {
    return this.ss.metaSignals();
  });
  viewSignals: Signal<Record<string, WritableSignal<any>>> = computed(() => {
    return this.ss.viewSignals();
  });
  appThemeConfig: Signal<{ [key in AppThemeType]: InputDropdownConfig }> = computed(() => {
    return {
      systemDefault: {
        name: this.cc.texts()['bottomSheet_themeSelect_systemDefault'],
      },
      dark: {
        name: this.cc.texts()['bottomSheet_themeSelect_dark'],
      },
      light: {
        name: this.cc.texts()['bottomSheet_themeSelect_light'],
      }
    }
  });

  getUtcDateString: Signal<string> = computed(() => {
    const d = this.cc.buildUpdatedDate();
    const day = String(d.getUTCDate()).padStart(2, '0');
    const month = String(d.getUTCMonth() + 1).padStart(2, '0');
    const year = d.getUTCFullYear();
    const hours = String(d.getUTCHours()).padStart(2, '0');
    const minutes = String(d.getUTCMinutes()).padStart(2, '0');
    return `${day}/${month}/${year} ${hours}:${minutes} UTC`;
  });

  constructor(
    public cc: CacheService,
    public ss: SettingsService,
    public vs: ViewSettingService,
    private dialog: MatDialog,
    private alertService: AlertService,
  ) {

  }

  openThemeDialog() {
    this.dialog.open(ThemeDialogComponent, {
      maxWidth: '400px',
      width: '100%',
    });
  }

  togglePasscode(event: boolean) {
    if (event) {
      this.openPinDialog('new');
    } else {
      this.ss.metaSignals()['isPasscodeEnabled'].set(false);
      this.ss.metaSignals()['passcode'].set(null);
      this.ss.updateUserSettings();
    }
  }

  openPinDialog(mode: 'new' | 'edit') {
    const pinDialog = this.dialog.open(PinDialogComponent, {
      maxWidth: '500px',
      width: '100%',
      disableClose: true,
      data: {
        type: mode,
        currentPin: mode === 'edit' ? this.ss.metaSignals()['passcode']() : null
      },
    });

    pinDialog.afterClosed().subscribe((pin: string | null) => {
      if (pin) {
        this.ss.metaSignals()['isPasscodeEnabled'].set(true);
        this.ss.metaSignals()['passcode'].set(pin);
        this.ss.updateUserSettings();

        if (mode === 'new') {
          this.alertService.toast('toast_mevolvePinSetSuccessfully_content');
        } else {
          this.alertService.toast('toast_mevolvePinUpdatedSuccessfully_content');
        }
      }
    });
  }
}
