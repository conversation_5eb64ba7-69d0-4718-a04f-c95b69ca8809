// import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from "@ngrx/signals";
// import { Hashtag, UserData, UserMetaData, UserResource, UserViewSettings } from "@app/_interfaces/user.interface";
// import { computed, inject, signal, WritableSignal } from "@angular/core";
// import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
// import { IndexDbService } from "@app/_services/index-db.service";
// import { CryptographyService } from "@app/_services/cryptography.service";
// import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
// import { StorageService } from "@app/_services/storage.servive";
// import { firstValueFrom } from "rxjs";
// import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
// import { UtilsService } from "@app/_services/utils.service";
// import { getNewViewSettings } from "@app/_datas/initial.data";
// import { DependencyService } from "@app/_services/dependency.service";
// import { CacheService } from "@app/_services/cache.service";
// import { Collection } from "@app/_types/collection.type";
// import { CollaboraterStatus, subscriptionPlan } from "@app/_types/generic.type";
// import { Feedback } from "@app/_interfaces/generic.interface";
// import { AlertService } from "@app/_services/alert.service";

// type UserState = {
//   user?: UserData;
//   userResource: UserResource | null;
//   userMetadata: UserMetaData | null;
//   viewSettings: UserViewSettings;
// };

// const initialState: UserState = {
//   user: undefined,
//   userResource: null,
//   userMetadata: null,
//   viewSettings: getNewViewSettings(),
// };

// export const UserStore = signalStore(
//   { providedIn: 'root' },

//   withState(initialState),

//   withComputed((store, ds = inject(DependencyService),) => ({
//     hashtags: computed(() => {
//       return store.userResource()?.tags ?? [];
//     }),

//     storageLimit: computed(() => {
//       const plan = store.userMetadata()?.subscriptionInfo.entitlement ?? 'free' as subscriptionPlan;
//       switch (plan) {
//         case 'free': return 100 * 1024 * 1024;
//         case 'basic': return 100 * 1024 * 1024;
//         case 'pro': return 100 * 1024 * 1024;
//         case 'plus': return 100 * 1024 * 1024;
//         default: return 100 * 1024 * 1024;
//       }
//     }),

//     tagMap: computed(() => {
//       return store.userResource()?.tags ? Object.fromEntries(store.userResource()!.tags.map(item => [item.id, item.tag])) : {};
//     }),

//     metaSignals: computed<Record<string, WritableSignal<any>>>(() => {
//       return {
//         // User info
//         name: signal(store.userMetadata()?.userInfo.name ?? ''),
//         email: signal(store.userMetadata()?.userInfo.email ?? ''),
//         pseudoName: signal(store.userMetadata()?.userInfo.pseudoName ?? null),
//         createdAt: signal(store.userMetadata()?.userInfo.createdAt ?? null),
//         storageUsed: signal(store.userMetadata()?.userInfo.storageUsed ?? 0),
//         tokensValidAfterTime: signal(store.userMetadata()?.userInfo.tokensValidAfterTime ?? null),

//         // Security
//         isBiometricEnabled: signal(store.userMetadata()?.securitySettings.isBiometricEnabled ?? false),
//         isPasscodeEnabled: signal(store.userMetadata()?.securitySettings.isPasscodeEnabled ?? false),
//         passcode: signal(store.userMetadata()?.securitySettings.passcode ?? null),
//         isUsingCustomKey: signal(store.userMetadata()?.userInfo.isUsingCustomKey ?? false),
//         resetPasscode: signal(store.userMetadata()?.userInfo.resetPasscode ?? false),

//         // Data
//         exportStatus: signal(store.userMetadata()?.dataBackupInfo.export.status ?? 'none'),
//         exportLastUpdatedAt: signal(store.userMetadata()?.dataBackupInfo.export.lastUpdatedAt ?? null),
//         importStatus: signal(store.userMetadata()?.dataBackupInfo.import.status ?? 'none'),
//         importLastUpdatedAt: signal(store.userMetadata()?.dataBackupInfo.import.lastUpdatedAt ?? null),

//         // Subscription
//         subscriptionPlan: signal(store.userMetadata()?.subscriptionInfo.entitlement ?? 'free'),
//         subscriptionState: signal(store.userMetadata()?.subscriptionInfo.subscriptionState ?? 'none'),
//         subscriptionExpDate: signal(store.userMetadata()?.subscriptionInfo.subscriptionExpDate ?? null),
//         subscriptionStartDate: signal(store.userMetadata()?.subscriptionInfo.subscriptionStartDate ?? null),
//         subscriptionType: signal(store.userMetadata()?.subscriptionInfo.subscriptionType ?? null),
//         storeType: signal(store.userMetadata()?.subscriptionInfo.storeType ?? null),
//         productId: signal(store.userMetadata()?.subscriptionInfo.productId ?? null),
//         unsubscribedAt: signal(store.userMetadata()?.subscriptionInfo.unsubscribedAt ?? null),

//         // Super subscription
//         superSubscription: signal(store.userMetadata()?.superSubscription ?? null),
//       };
//     }),

//     viewSignals: computed<Record<string, WritableSignal<any>>>(() => {
//       return {

//         // App settings
//         appTheme: signal(store.viewSettings().appSettings.appTheme),
//         themeColor: signal(store.viewSettings().appSettings.themeColor),
//         language: signal(store.viewSettings().appSettings.language),
//         supportLanguage: signal(store.viewSettings().appSettings.supportLanguage),
//         isVibrationEnabled: signal(store.viewSettings().appSettings.isVibrationEnabled),
//         isSpeechToTextEnabled: signal(store.viewSettings().appSettings.isSpeechToTextEnabled),

//         // Notes
//         noteGroupBy: signal(store.viewSettings().noteSettings.myNoteSettings.notesGroupBy),
//         noteShowType: signal(store.viewSettings().noteSettings.myNoteSettings.viewType),
//         noteShow: signal(ds.getShowValues(store.viewSettings().noteSettings.myNoteSettings)),
//         noteDescriptionType: signal(store.viewSettings().noteSettings.myNoteSettings.noteDescriptionType),
//         noteGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().noteSettings.myNoteSettings)),

//         sharedNoteGroupBy: signal(store.viewSettings().noteSettings.sharedNoteSettings.notesGroupBy),
//         sharedNoteShowType: signal(store.viewSettings().noteSettings.sharedNoteSettings.viewType),
//         sharedNoteShow: signal(ds.getShowValues(store.viewSettings().noteSettings.sharedNoteSettings)),
//         sharedNoteDescriptionType: signal(store.viewSettings().noteSettings.sharedNoteSettings.noteDescriptionType),
//         sharedNoteGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().noteSettings.sharedNoteSettings)),

//         savedNoteGroupBy: signal(store.viewSettings().noteSettings.savedNoteSettings.notesGroupBy),
//         savedNoteShowType: signal(store.viewSettings().noteSettings.savedNoteSettings.viewType),
//         savedNoteShow: signal(ds.getShowValues(store.viewSettings().noteSettings.savedNoteSettings)),
//         savedNoteDescriptionType: signal(store.viewSettings().noteSettings.savedNoteSettings.noteDescriptionType),
//         savedNoteGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().noteSettings.savedNoteSettings)),

//         // Lists
//         listGroupBy: signal(store.viewSettings().listSettings.myListSettings.groupByType),
//         listShowType: signal(store.viewSettings().listSettings.myListSettings.viewSettingType),
//         listShow: signal(ds.getShowValues(store.viewSettings().listSettings.myListSettings)),
//         listGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().listSettings.myListSettings)),
//         listItemShowType: signal(store.viewSettings().listSettings.myListItemSheetSettings.viewType),
//         listItemShow: signal(ds.getShowValues(store.viewSettings().listSettings.myListItemSheetSettings)),

//         sharedListGroupBy: signal(store.viewSettings().listSettings.sharedListSettings.groupByType),
//         sharedListShowType: signal(store.viewSettings().listSettings.sharedListSettings.viewSettingType),
//         sharedListShow: signal(ds.getShowValues(store.viewSettings().listSettings.sharedListSettings)),
//         sharedListGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().listSettings.sharedListSettings)),
//         sharedListItemShowType: signal(store.viewSettings().listSettings.sharedListItemSettings.viewType),
//         sharedListItemShow: signal(ds.getShowValues(store.viewSettings().listSettings.sharedListItemSettings)),

//         // Features

//         showTodoFeature: signal(store.viewSettings().featureSettings.showTodoFeature),
//         showNoteFeature: signal(store.viewSettings().featureSettings.showNoteFeature),
//         showListFeature: signal(store.viewSettings().featureSettings.showListFeature),

//         hiddenCalendarsMap: signal(store.viewSettings().featureSettings.hiddenCalendars),
//         hiddenCalendars: signal(Object.keys(store.viewSettings().featureSettings.hiddenCalendars)),
//         hiddenHabitsMap: signal(store.viewSettings().featureSettings.hiddenHabits),
//         hiddenHabits: signal(Object.keys(store.viewSettings().featureSettings.hiddenHabits)),
//         hiddenJournalsMap: signal(store.viewSettings().featureSettings.hiddenJournals),
//         hiddenJournals: signal(Object.keys(store.viewSettings().featureSettings.hiddenJournals)),
//         hiddenMoneytrackersMap: signal(store.viewSettings().featureSettings.hiddenMoneytrackers),
//         hiddenMoneytrackers: signal(Object.keys(store.viewSettings().featureSettings.hiddenMoneytrackers)),

//         // Entitys
//         entityGroupBy: signal(store.viewSettings().featureSettings.viewType),
//         entityShowType: signal(store.viewSettings().todaySettings.todayTabSettings.viewSettingType),
//         entityShow: signal(ds.getShowValues({ ...store.viewSettings().todaySettings.todayTabSettings, showLabel: store.viewSettings().featureSettings.showFeatureLabels })),
//         entityDescriptionType: signal(store.viewSettings().todaySettings.todayTabSettings.descriptionType),
//         userGoal: signal(store.viewSettings().featureSettings.userGoal),
//         showUserGoal: signal(store.viewSettings().featureSettings.showUserGoal),
//         showTimebox: signal(store.viewSettings().featureSettings.showTimebox),
//         showCalendarView: signal(store.viewSettings().featureSettings.showCalendarView),
//         hideCompletedItems: signal(store.viewSettings().featureSettings.hideCompletedItems),

//         // Overdue screen

//         overdueShowType: signal(store.viewSettings().todaySettings.overdueSettings.viewSettingType),
//         overdueShow: signal(ds.getShowValues(store.viewSettings().todaySettings.overdueSettings)),
//         overdueDescriptionType: signal(store.viewSettings().todaySettings.overdueSettings.descriptionType),

//         // Unscheduled screen

//         unscheduleShowType: signal(store.viewSettings().todaySettings.unscheduleSettings.viewSettingType),
//         unscheduleShow: signal(ds.getShowValues(store.viewSettings().todaySettings.unscheduleSettings)),
//         unscheduleDescriptionType: signal(store.viewSettings().todaySettings.unscheduleSettings.descriptionType),

//         // Past todo screen
//         pastTodoGroupBy: signal(store.viewSettings().pastSettings.pastTodoSettings.groupBy),
//         pastTodoGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastTodoSettings)),
//         pastTodoShowType: signal(store.viewSettings().pastSettings.pastTodoSettings.viewSettingType),
//         pastTodoShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastTodoSettings)),
//         pastTodoDescriptionType: signal(store.viewSettings().pastSettings.pastTodoSettings.descriptionType),

//         // Past journal screen
//         pastJournalGroupBy: signal(store.viewSettings().pastSettings.pastJournalSettings.groupBy),
//         pastJournalGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastJournalSettings)),
//         pastJournalShowType: signal(store.viewSettings().pastSettings.pastJournalSettings.viewSettingType),
//         pastJournalShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastJournalSettings)),
//         pastJournalDescriptionType: signal(store.viewSettings().pastSettings.pastJournalSettings.descriptionType),

//         // Past habit screen
//         pastHabitGroupBy: signal(store.viewSettings().pastSettings.pastHabitSettings.groupBy),
//         pastHabitGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastHabitSettings)),
//         pastHabitShowType: signal(store.viewSettings().pastSettings.pastHabitSettings.viewSettingType),
//         pastHabitShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastHabitSettings)),
//         pastHabitDescriptionType: signal(store.viewSettings().pastSettings.pastHabitSettings.descriptionType),

//         // Past money tracker screen
//         pastMoneyTrackerGroupBy: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.groupBySettings),
//         pastMoneyTrackerGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastMoneyTrackerSettings)),
//         pastMoneyTrackerShowType: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.viewSettingType),
//         pastMoneyTrackerShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastMoneyTrackerSettings)),
//         pastMoneyTrackerDescriptionType: signal(store.viewSettings().pastSettings.pastMoneyTrackerSettings.descriptionType),

//         // Past calendar event screen
//         pastCalendarEventGroupBy: signal(store.viewSettings().pastSettings.pastCalendarEventSettings.groupBy),
//         pastCalendarEventGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().pastSettings.pastCalendarEventSettings)),
//         pastCalendarEventShowType: signal(store.viewSettings().pastSettings.pastCalendarEventSettings.viewSettingType),
//         pastCalendarEventShow: signal(ds.getShowValues(store.viewSettings().pastSettings.pastCalendarEventSettings)),

//         // Future todo screen
//         futureTodoGroupBy: signal(store.viewSettings().futureSettings.futureTodoSettings.groupBy),
//         futureTodoGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureTodoSettings)),
//         futureTodoShowType: signal(store.viewSettings().futureSettings.futureTodoSettings.viewSettingType),
//         futureTodoShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureTodoSettings)),
//         futureTodoDescriptionType: signal(store.viewSettings().futureSettings.futureTodoSettings.descriptionType),

//         // Future journal screen
//         futureJournalGroupBy: signal(store.viewSettings().futureSettings.futureJournalSettings.groupBy),
//         futureJournalGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureJournalSettings)),
//         futureJournalShowType: signal(store.viewSettings().futureSettings.futureJournalSettings.viewSettingType),
//         futureJournalShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureJournalSettings)),
//         futureJournalDescriptionType: signal(store.viewSettings().futureSettings.futureJournalSettings.descriptionType),

//         // Future habit screen
//         futureHabitGroupBy: signal(store.viewSettings().futureSettings.futureHabitSettings.groupBy),
//         futureHabitGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureHabitSettings)),
//         futureHabitShowType: signal(store.viewSettings().futureSettings.futureHabitSettings.viewSettingType),
//         futureHabitShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureHabitSettings)),
//         futureHabitDescriptionType: signal(store.viewSettings().futureSettings.futureHabitSettings.descriptionType),

//         // Future money tracker screen
//         futureMoneyTrackerGroupBy: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.groupBySettings),
//         futureMoneyTrackerGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureMoneyTrackerSettings)),
//         futureMoneyTrackerShowType: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.viewSettingType),
//         futureMoneyTrackerShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureMoneyTrackerSettings)),
//         futureMoneyTrackerDescriptionType: signal(store.viewSettings().futureSettings.futureMoneyTrackerSettings.descriptionType),

//         // Future calendar event screen
//         futureCalendarEventGroupBy: signal(store.viewSettings().futureSettings.futureCalendarEventSettings.groupBy),
//         futureCalendarEventGroupedViewType: signal(ds.getGroupedViewType(store.viewSettings().futureSettings.futureCalendarEventSettings)),
//         futureCalendarEventShowType: signal(store.viewSettings().futureSettings.futureCalendarEventSettings.viewSettingType),
//         futureCalendarEventShow: signal(ds.getShowValues(store.viewSettings().futureSettings.futureCalendarEventSettings)),

//       };
//     }),

//   })),

//   withMethods((
//     store,
//     idbService = inject(IndexDbService),
//     cryptoService = inject(CryptographyService),
//     firebaseFunctionService = inject(FirebaseFunctionService),
//     ss = inject(StorageService),
//     cc = inject(CacheService),
//     utilsService = inject(UtilsService),
//     alertService = inject(AlertService)
//   ) => ({

//     addHashtag: async (hashtag: Hashtag) => {
//       const userResource = store.userResource();
//       if (!userResource) {
//         return;
//       }
//       const oldUserResource = await firstValueFrom(idbService.getEntityById('userResources', userResource.id));

//       userResource.tags.push(hashtag);
//       userResource.localUpdatedAt = new Date();
//       userResource.cloudUpdatedAt = null;
//       userResource.lastUpdatedAt = new Date();

//       const newUserResource = await idbService.update('userResources', userResource.id, userResource);

//       const syncRequest = cryptoService.preparePatchData(
//         [oldUserResource],
//         [newUserResource],
//         FirestoreCollection.UserResources
//       );
//       await firebaseFunctionService.uploadData(syncRequest);
//     },

//     updateHashtag: async (hashtag: Hashtag, isDelete: boolean = false) => {
//       const userResource = store.userResource();

//       if (!userResource) {
//         return;
//       }
//       const oldUserResource = await firstValueFrom(idbService.getEntityById('userResources', userResource.id));
//       if (isDelete) {
//         userResource.tags = userResource.tags.filter(tag => tag.id !== hashtag.id);
//       } else {
//         userResource.tags = userResource.tags.map(tag =>
//           tag.id === hashtag.id ? { ...tag, ...hashtag } : tag
//         );
//       }
//       userResource.localUpdatedAt = new Date();
//       userResource.cloudUpdatedAt = null;
//       userResource.lastUpdatedAt = new Date();

//       const newUserResource = await idbService.update('userResources', userResource.id, userResource);
//       const syncRequest = cryptoService.preparePatchData(
//         [oldUserResource],
//         [newUserResource],
//         FirestoreCollection.UserResources
//       );
//       await firebaseFunctionService.uploadData(syncRequest);
//     },

//     leaveCollaboratedEntity: async (status: CollaboraterStatus, entityId: string, collection: Collection) => {
//       cc.isLoading = true;
//       const payload = {
//         taskId: entityId,
//         userId: cc.user.uid,
//         hashedEmail: cc.user.hashedEmail,
//         leaveType: status,
//         collection,
//       };
//       try {
//         const response = await firebaseFunctionService.callableFunction('collaboration-leaveOrBlockTask', payload, true);
//         console.log('leaveCollaboratedEntity response', response);
//       } catch (error) {
//         console.log('error', error);
//       } finally {
//         setTimeout(() => {
//           cc.isLoading = false;
//         }, 2000);
//       }
//     },

//     sendFeedback: async (feedback: Feedback) => {
//       const feedbackPayload = {
//         ...feedback,
//         localUpdatedAt: new Date(),
//         cloudUpdatedAt: null,
//         createdAt: new Date(),
//         docVer: cc.dbVersion(),
//         docCollection: FirestoreCollection.UsersFeedback,
//         source: 'client',
//         sessionId: utilsService.getNewId(),
//         encData: {
//           dek: cryptoService.createEncryptedDocKey(),
//           encFields: []
//         }
//       };
//       console.log("i am called feedbackPayload--->>", feedbackPayload);
//       const syncRequest = cryptoService.prepareRawData({ ...feedbackPayload });
//       try {
//         await firebaseFunctionService.uploadData(syncRequest);
//       } catch (error) {
//         console.log('error', error);
//       }
//     },

//     async updateViewSettings() {
//       const viewSignals = store.viewSignals();
//       const viewSettings: UserViewSettings = {
//         ...store.viewSettings(),
//         appSettings: {
//           appTheme: viewSignals['appTheme'](),
//           themeColor: viewSignals['themeColor'](),
//           language: viewSignals['language'](),
//           supportLanguage: viewSignals['supportLanguage'](),
//           isVibrationEnabled: viewSignals['isVibrationEnabled'](),
//           isSpeechToTextEnabled: viewSignals['isSpeechToTextEnabled'](),
//         },
//         noteSettings: {
//           ...store.viewSettings().noteSettings,
//           myNoteSettings: {
//             noteDescriptionType: viewSignals['noteDescriptionType'](),
//             noteViewTime: viewSignals['noteShow']().includes('time'),
//             noteViewDate: viewSignals['noteShow']().includes('date'),
//             noteViewImage: viewSignals['noteShow']().includes('attachments'),
//             noteViewMood: viewSignals['noteShow']().includes('mood'),
//             noteViewTags: viewSignals['noteShow']().includes('hashtag'),
//             notesGroupBy: viewSignals['noteGroupBy'](),
//             viewType: viewSignals['noteShowType'](),
//             collapsedView: viewSignals['noteGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['noteGroupedViewType']().includes('showCounts'),
//             showMemberCount: viewSignals['noteShow']().includes('collaboratorsCount'),
//             showFilterRow: false,
//           },
//           sharedNoteSettings: {
//             noteDescriptionType: viewSignals['sharedNoteDescriptionType'](),
//             noteViewTime: viewSignals['sharedNoteShow']().includes('time'),
//             noteViewDate: viewSignals['sharedNoteShow']().includes('date'),
//             noteViewImage: viewSignals['sharedNoteShow']().includes('attachments'),
//             noteViewMood: viewSignals['sharedNoteShow']().includes('mood'),
//             showAccess: viewSignals['sharedNoteShow']().includes('access'),
//             notesGroupBy: viewSignals['sharedNoteGroupBy'](),
//             viewType: viewSignals['sharedNoteShowType'](),
//             collapsedView: viewSignals['sharedNoteGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['sharedNoteGroupedViewType']().includes('showCounts'),
//             showFilterRow: false,
//           },
//         },
//         listSettings: {
//           ...store.viewSettings().listSettings,
//           myListSettings: {
//             showDescription: viewSignals['listShow']().includes('description'),
//             itemCount: viewSignals['listShow']().includes('itemCount'),
//             showCollaboratorsCount: viewSignals['listShow']().includes('collaboratorsCount'),
//             showInvitedUsersCount: viewSignals['listShow']().includes('invitedUsersCount'),
//             showAwaitingUserCount: viewSignals['listShow']().includes('awaitingUserCount'),
//             showHashtags: viewSignals['listShow']().includes('hashtag'),
//             groupByType: viewSignals['listGroupBy'](),
//             collapsedView: viewSignals['listGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['listGroupedViewType']().includes('showCounts'),
//             viewSettingType: viewSignals['listShowType'](),
//           },
//           myListItemSheetSettings: {
//             showDescription: viewSignals['listItemShow']().includes('description'),
//             viewType: viewSignals['listItemShowType'](),
//             showLastUpdatedBy: viewSignals['listItemShow']().includes('lastUpdatedBy'),
//             showLastUpdatedAt: viewSignals['listItemShow']().includes('lastUpdatedAt'),
//           },
//           sharedListSettings: {
//             showDescription: viewSignals['sharedListShow']().includes('description'),
//             itemCount: viewSignals['sharedListShow']().includes('itemCount'),
//             showAccess: viewSignals['sharedListShow']().includes('access'),
//             showHashtags: viewSignals['sharedListShow']().includes('hashtag'),
//             groupByType: viewSignals['sharedListGroupBy'](),
//             collapsedView: viewSignals['sharedListGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['sharedListGroupedViewType']().includes('showCounts'),
//             viewSettingType: viewSignals['sharedListShowType'](),
//           },
//           sharedListItemSettings: {
//             showDescription: viewSignals['sharedListItemShow']().includes('description'),
//             viewType: viewSignals['sharedListItemShowType'](),
//             showLastUpdatedBy: viewSignals['sharedListItemShow']().includes('lastUpdatedBy'),
//             showLastUpdatedAt: viewSignals['sharedListItemShow']().includes('lastUpdatedAt'),
//           },
//         },
//         featureSettings: {
//           ...store.viewSettings().featureSettings,
//           hiddenCalendars: viewSignals['hiddenCalendarsMap'](),
//           hiddenHabits: viewSignals['hiddenHabitsMap'](),
//           hiddenJournals: viewSignals['hiddenJournalsMap'](),
//           hiddenMoneytrackers: viewSignals['hiddenMoneytrackersMap'](),
//           viewType: viewSignals['entityGroupBy'](),
//           userGoal: viewSignals['userGoal'](),
//           showUserGoal: viewSignals['showUserGoal'](),
//           showTimebox: viewSignals['showTimebox'](),
//           showCalendarView: viewSignals['showCalendarView'](),
//           hideCompletedItems: viewSignals['hideCompletedItems'](),
//           showFeatureLabels: viewSignals['entityShow']().includes('label'),
//           showTodoFeature: viewSignals['showTodoFeature'](),
//           showNoteFeature: viewSignals['showNoteFeature'](),
//           showListFeature: viewSignals['showListFeature'](),
//         },
//         todaySettings: {
//           todayTabSettings: {
//             ...store.viewSettings().todaySettings.todayTabSettings,
//             descriptionType: viewSignals['entityDescriptionType'](),
//             viewSettingType: viewSignals['entityShowType'](),
//             showMood: viewSignals['entityShow']().includes('mood'),
//             showTime: viewSignals['entityShow']().includes('time'),
//             showReminder: viewSignals['entityShow']().includes('reminder'),
//             showDuration: viewSignals['entityShow']().includes('duration'),
//             showRepeat: viewSignals['entityShow']().includes('repeat'),
//             showChecklist: viewSignals['entityShow']().includes('checklist'),
//             showImage: viewSignals['entityShow']().includes('attachments'),
//             showTags: viewSignals['entityShow']().includes('hashtag'),
//             showHabitResponse: viewSignals['entityShow']().includes('habitResponse')
//           },
//           overdueSettings: {
//             descriptionType: viewSignals['overdueDescriptionType'](),
//             viewSettingType: viewSignals['overdueShowType'](),
//             showTime: viewSignals['overdueShow']().includes('time'),
//             showReminder: viewSignals['overdueShow']().includes('reminder'),
//             showDuration: viewSignals['overdueShow']().includes('duration'),
//             showRepeat: viewSignals['overdueShow']().includes('repeat'),
//             showChecklist: viewSignals['overdueShow']().includes('checklist'),
//             showImage: viewSignals['overdueShow']().includes('attachments'),
//             showTags: viewSignals['overdueShow']().includes('hashtag'),
//           },
//           unscheduleSettings: {
//             descriptionType: viewSignals['unscheduleDescriptionType'](),
//             viewSettingType: viewSignals['unscheduleShowType'](),
//             showCompletedAt: viewSignals['unscheduleShow']().includes('completedAt'),
//             showChecklist: viewSignals['unscheduleShow']().includes('checklist'),
//             showImage: viewSignals['unscheduleShow']().includes('attachments'),
//             showTags: viewSignals['unscheduleShow']().includes('hashtag'),
//           },
//         },
//         pastSettings: {
//           ...store.viewSettings().pastSettings,
//           pastTodoSettings: {
//             ...store.viewSettings().pastSettings.pastTodoSettings,
//             groupBy: viewSignals['pastTodoGroupBy'](),
//             viewSettingType: viewSignals['pastTodoShowType'](),
//             collapsedView: viewSignals['pastTodoGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['pastTodoGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['pastTodoDescriptionType'](),
//             showDate: viewSignals['pastTodoShow']().includes('date'),
//             showTime: viewSignals['pastTodoShow']().includes('time'),
//             showReminder: viewSignals['pastTodoShow']().includes('reminder'),
//             showDuration: viewSignals['pastTodoShow']().includes('duration'),
//             showRepeat: viewSignals['pastTodoShow']().includes('repeat'),
//             showChecklist: viewSignals['pastTodoShow']().includes('checklist'),
//             showImage: viewSignals['pastTodoShow']().includes('attachments'),
//             showTags: viewSignals['pastTodoShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['pastTodoShow']().includes('emptyDays')
//           },
//           pastJournalSettings: {
//             ...store.viewSettings().pastSettings.pastJournalSettings,
//             groupBy: viewSignals['pastJournalGroupBy'](),
//             viewSettingType: viewSignals['pastJournalShowType'](),
//             collapsedView: viewSignals['pastJournalGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['pastJournalGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['pastJournalDescriptionType'](),
//             showDate: viewSignals['pastJournalShow']().includes('date'),
//             showTime: viewSignals['pastJournalShow']().includes('time'),
//             showReminder: viewSignals['pastJournalShow']().includes('reminder'),
//             showDuration: viewSignals['pastJournalShow']().includes('duration'),
//             showRepeat: viewSignals['pastJournalShow']().includes('repeat'),
//             showImage: viewSignals['pastJournalShow']().includes('attachments'),
//             showMood: viewSignals['pastJournalShow']().includes('mood'),
//             showTags: viewSignals['pastJournalShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['pastJournalShow']().includes('emptyDays'),
//             showInvalidEntries: viewSignals['pastJournalShow']().includes('invalidEntry'),
//           },
//           pastHabitSettings: {
//             ...store.viewSettings().pastSettings.pastHabitSettings,
//             groupBy: viewSignals['pastHabitGroupBy'](),
//             viewSettingType: viewSignals['pastHabitShowType'](),
//             collapsedView: viewSignals['pastHabitGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['pastHabitGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['pastHabitDescriptionType'](),
//             showHabitResponse: viewSignals['pastHabitShow']().includes('habitResponse'),
//             showDate: viewSignals['pastHabitShow']().includes('date'),
//             showTime: viewSignals['pastHabitShow']().includes('time'),
//             showReminder: viewSignals['pastHabitShow']().includes('reminder'),
//             showDuration: viewSignals['pastHabitShow']().includes('duration'),
//             showRepeat: viewSignals['pastHabitShow']().includes('repeat'),
//             showImage: viewSignals['pastHabitShow']().includes('attachments'),
//             showTags: viewSignals['pastHabitShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['pastHabitShow']().includes('emptyDays'),
//             showInvalidEntries: viewSignals['pastHabitShow']().includes('invalidEntry'),
//           },
//           pastMoneyTrackerSettings: {
//             ...store.viewSettings().pastSettings.pastMoneyTrackerSettings,
//             collapsedView: viewSignals['pastMoneyTrackerGroupedViewType']().includes('collapsedView'),
//             descriptionType: viewSignals['pastMoneyTrackerDescriptionType'](),
//             groupBySettings: viewSignals['pastMoneyTrackerGroupBy'](),
//             viewSettingType: viewSignals['pastMoneyTrackerShowType'](),
//             showImage: viewSignals['pastMoneyTrackerShow']().includes('attachments'),
//             showHashtag: viewSignals['pastMoneyTrackerShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['pastMoneyTrackerShow']().includes('emptyDays'),
//             showNetAmount: viewSignals['pastMoneyTrackerGroupedViewType']().includes('showNetAmount'),
//             showSetupTitle: viewSignals['pastMoneyTrackerShow']().includes('setup'),
//           },
//           pastCalendarEventSettings: {
//             ...store.viewSettings().pastSettings.pastCalendarEventSettings,
//             groupBy: viewSignals['pastCalendarEventGroupBy'](),
//             viewSettingType: viewSignals['pastCalendarEventShowType'](),
//             collapsedView: viewSignals['pastCalendarEventGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['pastCalendarEventGroupedViewType']().includes('showCounts'),
//             showTime: viewSignals['pastCalendarEventShow']().includes('time'),
//             showReminder: viewSignals['pastCalendarEventShow']().includes('reminder'),
//             showDuration: viewSignals['pastCalendarEventShow']().includes('duration'),
//             showRepeat: viewSignals['pastCalendarEventShow']().includes('repeat'),
//             showCalendarName: viewSignals['pastCalendarEventShow']().includes('calendarName'),
//           },
//         },
//         futureSettings: {
//           ...store.viewSettings().futureSettings,
//           futureTodoSettings: {
//             ...store.viewSettings().futureSettings.futureTodoSettings,
//             groupBy: viewSignals['futureTodoGroupBy'](),
//             viewSettingType: viewSignals['futureTodoShowType'](),
//             collapsedView: viewSignals['futureTodoGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['futureTodoGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['futureTodoDescriptionType'](),
//             showTime: viewSignals['futureTodoShow']().includes('time'),
//             showReminder: viewSignals['futureTodoShow']().includes('reminder'),
//             showDuration: viewSignals['futureTodoShow']().includes('duration'),
//             showRepeat: viewSignals['futureTodoShow']().includes('repeat'),
//             showChecklist: viewSignals['futureTodoShow']().includes('checklist'),
//             showImage: viewSignals['futureTodoShow']().includes('attachments'),
//             showTags: viewSignals['futureTodoShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['futureTodoShow']().includes('emptyDays'),
//           },
//           futureJournalSettings: {
//             ...store.viewSettings().futureSettings.futureJournalSettings,
//             groupBy: viewSignals['futureJournalGroupBy'](),
//             viewSettingType: viewSignals['futureJournalShowType'](),
//             collapsedView: viewSignals['futureJournalGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['futureJournalGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['futureJournalDescriptionType'](),
//             showTime: viewSignals['futureJournalShow']().includes('time'),
//             showReminder: viewSignals['futureJournalShow']().includes('reminder'),
//             showDuration: viewSignals['futureJournalShow']().includes('duration'),
//             showRepeat: viewSignals['futureJournalShow']().includes('repeat'),
//             showImage: viewSignals['futureJournalShow']().includes('attachments'),
//             showTags: viewSignals['futureJournalShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['futureJournalShow']().includes('emptyDays'),
//             showInvalidEntries: viewSignals['futureJournalShow']().includes('invalidEntry'),
//             showMood: viewSignals['futureJournalShow']().includes('mood'),
//           },
//           futureHabitSettings: {
//             ...store.viewSettings().futureSettings.futureHabitSettings,
//             groupBy: viewSignals['futureHabitGroupBy'](),
//             viewSettingType: viewSignals['futureHabitShowType'](),
//             collapsedView: viewSignals['futureHabitGroupedViewType']().includes('collapsedView'),
//             showCounts: viewSignals['futureHabitGroupedViewType']().includes('showCounts'),
//             descriptionType: viewSignals['futureHabitDescriptionType'](),
//             showHabitResponse: viewSignals['futureHabitShow']().includes('habitResponse'),
//             showDate: viewSignals['futureHabitShow']().includes('date'),
//             showTime: viewSignals['futureHabitShow']().includes('time'),
//             showReminder: viewSignals['futureHabitShow']().includes('reminder'),
//             showDuration: viewSignals['futureHabitShow']().includes('duration'),
//             showRepeat: viewSignals['futureHabitShow']().includes('repeat'),
//             showImage: viewSignals['futureHabitShow']().includes('attachments'),
//             showTags: viewSignals['futureHabitShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['futureHabitShow']().includes('emptyDays'),
//             showInvalidEntries: viewSignals['futureHabitShow']().includes('invalidEntry')
//           },
//           futureMoneyTrackerSettings: {
//             ...store.viewSettings().futureSettings.futureMoneyTrackerSettings,
//             collapsedView: viewSignals['futureMoneyTrackerGroupedViewType']().includes('collapsedView'),
//             descriptionType: viewSignals['futureMoneyTrackerDescriptionType'](),
//             groupBySettings: viewSignals['futureMoneyTrackerGroupBy'](),
//             viewSettingType: viewSignals['futureMoneyTrackerShowType'](),
//             showImage: viewSignals['futureMoneyTrackerShow']().includes('attachments'),
//             showHashtag: viewSignals['futureMoneyTrackerShow']().includes('hashtag'),
//             showEmptyDays: viewSignals['futureMoneyTrackerShow']().includes('emptyDays'),
//             showNetAmount: viewSignals['futureMoneyTrackerGroupedViewType']().includes('showNetAmount'),
//             showSetupTitle: viewSignals['futureMoneyTrackerShow']().includes('setup'),
//           },
//           futureCalendarEventSettings: {
//             ...store.viewSettings().futureSettings.futureCalendarEventSettings,
//             collapsedView: viewSignals['futureCalendarEventGroupedViewType']().includes('collapsedView'),
//             groupBy: viewSignals['futureCalendarEventGroupBy'](),
//             showCalendarName: viewSignals['futureCalendarEventShow']().includes('calendarName'),
//             showCounts: viewSignals['futureCalendarEventGroupedViewType']().includes('showCounts'),
//             showTime: viewSignals['futureCalendarEventShow']().includes('time'),
//             showReminder: viewSignals['futureCalendarEventShow']().includes('reminder'),
//             showDuration: viewSignals['futureCalendarEventShow']().includes('duration'),
//             showRepeat: viewSignals['futureCalendarEventShow']().includes('repeat'),
//             viewSettingType: viewSignals['futureCalendarEventShowType'](),
//           },
//         },
//       };

//       const oldViewSettings = await firstValueFrom(idbService.getEntityById('viewSettings', viewSettings.id));
//       const newViewSettings = await idbService.update('viewSettings', viewSettings.id, viewSettings);
//       newViewSettings!.localUpdatedAt = new Date();
//       newViewSettings!.cloudUpdatedAt = null;

//       const syncRequest = cryptoService.preparePatchData(
//         [oldViewSettings],
//         [newViewSettings],
//         FirestoreCollection.ViewSettings
//       );

//       const res = await firebaseFunctionService.uploadData(syncRequest);
//       console.log(" i am called res-->>", res);
//       // if(res.success)
//     },

//     createDocEncKey(): string {
//       return cryptoService.createEncryptedDocKey();
//     },

//     async updateUserSettings() {
//       const metaSignals = store.metaSignals();
//       const metaData = store.userMetadata();
//       if (!metaData) return;
//       const userMetadata: UserMetaData = {
//         ...metaData,
//         securitySettings: {
//           isBiometricEnabled: metaSignals['isBiometricEnabled'](),
//           isPasscodeEnabled: metaSignals['isPasscodeEnabled'](),
//           passcode: metaSignals['passcode'](),
//         },
//         userInfo: {
//           ...metaData.userInfo,
//           name: metaSignals['name'](),
//           email: metaSignals['email'](),
//           pseudoName: metaSignals['pseudoName'](),
//           isUsingCustomKey: metaSignals['isUsingCustomKey']()
//         }
//       };

//       const oldMetaSettings = await firstValueFrom(idbService.getEntityById('usersMetadata', userMetadata.id));
//       const newMetaSettings = await idbService.update('usersMetadata', userMetadata.id, userMetadata);
//       newMetaSettings!.localUpdatedAt = new Date();
//       newMetaSettings!.cloudUpdatedAt = null;

//       const syncRequest = cryptoService.preparePatchData(
//         [oldMetaSettings],
//         [newMetaSettings],
//         FirestoreCollection.ViewSettings
//       );

//       const res = await firebaseFunctionService.uploadData(syncRequest);
//       console.log(" i am called res-->>", res);
//       // if(res.success)
//     },
//   })),

//   withHooks({
//     async onInit(
//       store,
//       idbService = inject(IndexDbService),
//       ss = inject(StorageService),
//       cryptoService = inject(CryptographyService)
//     ) {
//       const userData = ss.getUser();

//       if (userData) {
//         const user = {
//           uid: userData.uid,
//           email: userData.userInfo.email,
//           name: userData.userInfo.name,
//           pseudoName: userData.userInfo.pseudoName,
//           isNewUser: false,
//           hashedEmail: ss.getHashedEmail()
//         };
//         patchState(store, { user: user });
//       // }

//       idbService.userResource$.pipe(takeUntilDestroyed()).subscribe(data => {
//         if (data) {
//           patchState(store, { userResource: data[0] });
//           console.log('userResource =======================', data[0]);
//         }
//       });

//       idbService.userMetadata$.pipe(takeUntilDestroyed()).subscribe(data => {
//         if (data) {
//           patchState(store, { userMetadata: data[0] });
//           console.log('userMetadata =======================', data[0]);
//         }
//       });

//       idbService.releaseConfig$.pipe(takeUntilDestroyed()).subscribe(data => {
//         if (data) {
//           console.log('releaseConfig =======================', data[0]);
//         }
//       });

//       idbService.viewSettings$.pipe(takeUntilDestroyed()).subscribe(data => {
//         if (data) {
//           console.log('viewSettings =======================', data[0]);
//           const viewSettings = data[0];
//           patchState(store, { viewSettings: viewSettings });
//         }
//       });
//     },
//   }),
// );
