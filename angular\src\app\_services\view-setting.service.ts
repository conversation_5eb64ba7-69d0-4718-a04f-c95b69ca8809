import { computed, Injectable, Signal } from '@angular/core';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { SettingsService } from './settings.service';

@Injectable({
    providedIn: 'root',
})

export class ViewSettingService {

    constructor(private ss: SettingsService) {

    }
    // Generic

    showTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            compact: {
                name: this.ss.texts()['dropdown_show_compact'],
            },
            custom: {
                name: this.ss.texts()['dropdown_show_custom'],
            }
        }
    })

    groupedViewTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            collapsedView: {
                icon: 'collapse',
                name: this.ss.texts()['dropdown_groupSettings_collapsedView'],
            },
            showCounts: {
                icon: 'count',
                name: this.ss.texts()['dropdown_groupSettings_showCounts'],
            },
        }
    })

    // Notes

    noteGroupByConfig: { [key: string]: InputDropdownConfig } = {
        none: {
            icon: 'none',
            name: 'None',
        },
        date: {
            icon: 'calendar',
            name: 'Date',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        },
        mood: {
            icon: 'smiley',
            name: 'Mood',
        }
    }

    noteShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
            signalName: 'noteDescriptionType',
            config: {
                none: {
                    icon: 'none',
                    name: 'None',
                },
                short: {
                    icon: 'short',
                    name: 'Short',
                },
                full: {
                    icon: 'full',
                    name: 'Full',
                }
            },
            multiple: false
        },
        mood: {
            icon: 'smiley',
            name: 'Mood',
        },
        date: {
            icon: 'calendar',
            name: 'Date',
        },
        time: {
            icon: 'clock',
            name: 'Time',
        },
        attachments: {
            icon: 'attachment',
            name: 'Attachments',
        },
        collaboratorsCount: {
            icon: 'users',
            name: 'Member Count',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        },
        access: {
            icon: 'key',
            name: 'Access',
        }
    }

    // Lists

    listGroupByConfig: { [key: string]: InputDropdownConfig } = {
        none: {
            icon: 'none',
            name: 'None',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        }
    }

    listShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
        },
        itemCount: {
            icon: 'circleCheck',
            name: 'Item Count',
        },
        collaboratorsCount: {
            icon: 'users',
            name: 'Collaborators Count',
        },
        hashtag: {
            icon: 'hashtag',
            name: 'Hashtag',
        },
        access: {
            icon: 'key',
            name: 'Access',
        }
    }

    listItemAddonConfig: { [key: string]: InputDropdownConfig } = {
        checkbox: {
            icon: 'circleCheck',
            name: 'Checkbox',
        },
        customText: {
            icon: 'text',
            name: 'Custom Text',
        },
        none: {
            icon: 'none',
            name: 'None',
        }
    }

    listItemShowConfig: { [key: string]: InputDropdownConfig } = {
        description: {
            icon: 'description',
            name: 'Description',
        },
        lastUpdatedBy: {
            icon: 'edit',
            name: 'Last Updated By',
        },
        lastUpdatedAt: {
            icon: 'clock',
            name: 'Last Updated At',
        }
    }

    listItemShowTypeConfig: { [key: string]: InputDropdownConfig } = {
        compact: {
            name: 'Compact',
        },
        custom: {
            name: 'Custom',
        }
    }

    // Entities

    entityGroupByConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            chronological: {
                icon: 'none',
                name: this.ss.texts()['screen_common_none'],
            },
            category: {
                icon: 'feature',
                name: this.ss.texts()['dropdown_groupBy_feature'],
            },
            date: {
                icon: 'calendar',
                name: this.ss.texts()['screen_common_date'],
            },
            hashtag: {
                icon: 'hashtag',
                name: this.ss.texts()['screen_common_hashtag'],
            },
            mood: {
                icon: 'smiley',
                name: this.ss.texts()['dropdown_groupBy_mood'],
            },
            transactionType: {
                icon: 'entry',
                name: this.ss.texts()['dropdown_groupBy_entry'],
            },
            setup: {
                icon: 'widget',
                name: this.ss.texts()['dropdown_groupBy_setup'],
            }
        }
    })

    entityGroupedViewTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            collapsedView: {
                icon: 'collapse',
                name: this.ss.texts()['dropdown_groupSettings_collapsedView'],
            },
            showCounts: {
                icon: 'count',
                name: this.ss.texts()['dropdown_groupSettings_showCounts'],
            },
            showNetAmount: {
                icon: 'count',
                name: this.ss.texts()['dropdown_groupSettings_showNetAmount'],
            }
        }
    })

    entityShowConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            description: {
                icon: 'description',
                name: 'Description',
                signalName: 'entityDescriptionType',
                config: {
                    none: {
                        icon: 'none',
                        name: this.ss.texts()['screen_common_none'],
                    },
                    short: {
                        icon: 'short',
                        name: this.ss.texts()['dropdown_description_short'],
                    },
                    full: {
                        icon: 'full',
                        name: this.ss.texts()['dropdown_description_full'],
                    }
                },
                multiple: false
            },
            completedAt: {
                icon: 'circleCheck',
                name: this.ss.texts()['bottomSheet_viewConfiguration_completedAt'],
            },
            habitResponse: {
                icon: 'response',
                name: this.ss.texts()['bottomSheet_viewConfiguration_habitResponse'],
            },
            setup: {
                icon: 'widget',
                name: this.ss.texts()['dropdown_groupBy_setup'],
            },
            date: {
                icon: 'calendar',
                name: this.ss.texts()['bottomSheet_viewConfiguration_date'],
            },
            mood: {
                icon: 'smiley',
                name: this.ss.texts()['bottomSheet_viewConfiguration_mood'],
            },
            time: {
                icon: 'clock',
                name: this.ss.texts()['screen_common_time'],
            },
            reminder: {
                icon: 'bell',
                name: this.ss.texts()['bottomSheet_viewConfiguration_reminder'],
            },
            duration: {
                icon: 'timer',
                name: this.ss.texts()['bottomSheet_viewConfiguration_duration'],
            },
            repeat: {
                icon: 'repeat',
                name: this.ss.texts()['screen_common_repeat'],
            },
            checklist: {
                icon: 'checklist',
                name: this.ss.texts()['bottomSheet_viewConfiguration_checkList'],
            },
            attachments: {
                icon: 'attachment',
                name: this.ss.texts()['screen_common_attachment'],
            },
            calendarName: {
                icon: 'calendarIntegration',
                name: this.ss.texts()['bottomSheet_viewConfiguration_calendarName'],
            },
            label: {
                icon: 'label',
                name: this.ss.texts()['bottomSheet_viewConfiguration_featureLabels'],
            },
            hashtag: {
                icon: 'hashtag',
                name: this.ss.texts()['screen_common_hashtag'],
            },
            emptyDays: {
                icon: 'emptyCalendar',
                name: this.ss.texts()['bottomSheet_viewConfiguration_emptyDays'],
            },
            invalidEntry: {
                icon: 'circleInvalid',
                name: this.ss.texts()['bottomSheet_viewConfiguration_invalidEntry'],
            }
        }
    });

    entityDescriptionTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            none: {
                icon: 'none',
                name: this.ss.texts()['screen_common_none'],
            },
            short: {
                icon: 'short',
                name: this.ss.texts()['dropdown_description_short'],
            },
            full: {
                icon: 'full',
                name: this.ss.texts()['dropdown_description_full'],
            }
        }
    })

    // Collaborator Role

    collaboratorRoleConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            viewer: {
                name: this.ss.texts()['dropdown_permission_viewer'],
                description: this.ss.texts()['dropdown_permission_viewerContent'],
            },
            editor: {
                name: this.ss.texts()['dropdown_permission_editor'],
                description: this.ss.texts()['dropdown_permission_editorContent'],
            },
        }
    });

    // Week day config

    weekDayConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            SU: {
                displayName: this.ss.texts()['screen_common_daySunday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_sunday'],
            },
            MO: {
                displayName: this.ss.texts()['screen_common_dayMonday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_monday'],
            },
            TU: {
                displayName: this.ss.texts()['screen_common_dayTuesday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_tuesday'],
            },
            WE: {
                displayName: this.ss.texts()['screen_common_dayWednesday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_wednesday'],
            },
            TH: {
                displayName: this.ss.texts()['screen_common_dayThursday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_thursday'],
            },
            FR: {
                displayName: this.ss.texts()['screen_common_dayFriday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_friday'],
            },
            SA: {
                displayName: this.ss.texts()['screen_common_daySaturday'],
                name: this.ss.texts()['bottomSheet_selectWeekDays_saturday'],
            }
        }
    });

    // Ordinal Condig
    
    ordinalConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            '1': {
                name: this.ss.texts()['bottomSheet_selectWeeks_1stWeek'],
            },
            '2': {
                name: this.ss.texts()['bottomSheet_selectWeeks_2ndWeek'],
            },
            '3': {
                name: this.ss.texts()['bottomSheet_selectWeeks_3rdWeek'],
            },
            '4': {
                name: this.ss.texts()['bottomSheet_selectWeeks_4thWeek'],
            },
            '5': {
                name: this.ss.texts()['bottomSheet_selectWeeks_5thWeek'],
            },
        }
    });

    // Repeat Type Config

    repeatTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            OFF: {
                name: this.ss.texts()['bottomSheet_selectRepeat_off'],
            },
            DAILY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_daily'],
            },
            WEEK_WORKING_DAY: {
                name: this.ss.texts()['screen_common_repeatMondayToFriday'],
            },
            WEEKLY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_weekly'],
            },
            MONTHLY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_monthly'],
            },
            MONTHLY_NTH_DAY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_monthlyNthDay'],
            },
            MONTHLY_LAST_DAY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_monthlyLastDay'],
            },
            YEARLY: {
                name: this.ss.texts()['bottomSheet_selectRepeat_yearly'],
            }
        }
    })

    // Remind Type Config

    remindTypeConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
        return {
            BEFORE: {
                name: this.ss.texts()['dropdown_remindOption_before'],
            },
            ON_TIME: {
                name: this.ss.texts()['dropdown_remindOption_onTime'],
            },
            AFTER: {
                name: this.ss.texts()['dropdown_remindOption_after'],
            }
        }
    })
}
