import { computed, inject, Injectable, Signal, signal, WritableSignal } from '@angular/core';
import { HabitTimerAnswer, HabitTimerState } from '@app/_interfaces/habit.interface';
import { StorageService } from './storage.servive';
import { HabitStore } from '@app/_stores';
import { getCustomDate, getNumStrId } from '@app/_utils/utils';
import { AlertToneType, TimerStopType } from '@app/_types/generic.type';
import { HabitTimerEndDialogComponent } from '@app/components/features/habits/habit-timer-end-dialog/habit-timer-end-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { CacheService } from './cache.service';

@Injectable({
    providedIn: 'root',
})

export class HabitTimerService {

    readonly habitStore = inject(HabitStore);
    timers: WritableSignal<{ [key in string]: HabitTimerState }> = signal({});
    timerTickInterval: any;
    addedTimers: Signal<HabitTimerState[]> = computed(() => {
        const timers = this.timers();
        return Object.values(timers);
    });
    timerTick: WritableSignal<number> = signal(0);
    alertInterval: any;

    constructor(private storageService: StorageService, private dialog: MatDialog, public cc: CacheService) {

    }

    init() {
        const timers = this.storageService.getHabitTimers();
        this.timers.set(timers);
        if (Object.keys(timers).length > 0) {
            this.runTimerInterval();
        }
    }

    startTimer(dateString: string, setupId: string, totalSeconds: number) {
        const id = `${setupId}&&&${dateString}`;
        const state: HabitTimerState = {
            id,
            setupId,
            dateString,
            startTimestamp: new Date(),
            totalSecondsSpent: 0,
            totalSeconds: totalSeconds,
        };

        // Save to map
        const timers = { ...this.timers(), [id]: state };
        this.storageService.setHabitTimers(timers);
        this.timers.set(timers);
        if (!this.timerTickInterval) {
            this.runTimerInterval();
        }
    };

    runTimerInterval() {
        this.timerTickInterval = setInterval(() => {
            this.runTimer();
            this.timerTick.set(this.timerTick() + 1);
        }, 1000);
    }

    runTimer() {
        const allTimers = this.timers();
        console.log('RUNNING TIMER', allTimers);
        if (Object.keys(allTimers).length === 0) return;
        const timerKeys = Object.keys(allTimers);
        timerKeys.forEach(id => {
            const current = allTimers[id];

            if (!current) return;

            const now = new Date();
            const elapsedMs = now.getTime() - new Date(current.startTimestamp).getTime();
            const elapsedSeconds = Math.floor(elapsedMs / 1000);

            const [setupId, dateString] = id.split('&&&');

            const habit = this.habitStore.habitMap().setups[setupId]?.[dateString] || null;
            const setup = this.habitStore.idToSetup()[setupId];
            const totalSeconds = setup?.timerGoal ? this.parseMinutes(setup.timerGoal).totalSeconds : 0;
            current.totalSeconds = totalSeconds;

            console.log('HABIT', habit);
            const totalSecondsSpent = this.getTotalSecondsSpent(habit?.timerAnswer || []) + elapsedSeconds;
            current.totalSecondsSpent = totalSecondsSpent;

            const timerStopType: TimerStopType = setup?.timerStopType || 'onRoundEnd';
            const repeatCount = setup?.durationRepeatCount || 1;
            const currentRound = this.getCurrentRound(totalSecondsSpent, totalSeconds, repeatCount);

            const timers = { ...this.timers(), [id]: current };
            this.timers.set(timers);

            if (totalSeconds && ((timerStopType === 'onRoundEnd' && (current.totalSecondsSpent >= (totalSeconds * currentRound))) || (timerStopType === 'onGoalReached' && (current.totalSecondsSpent >= (totalSeconds * repeatCount))))
            ) {
                console.log('STOPPING TIMER', id);
                this.stopTimer(id);
            }
        });
    };

    stopTimer(id: string) {
        const existingTimers = this.timers();
        const state = existingTimers[id];
        const startTimerDate = state.startTimestamp;
        if (!state || !startTimerDate) return;

        const habitTimerAnswer = {
            id: getNumStrId(),
            startTimestamp: startTimerDate,
            endTimestamp: new Date(),
            durationRepeatCount: 1
        }

        const [setupId, dateString] = id.split('&&&');

        const habit = this.habitStore.habitMap().setups[setupId]?.[dateString] || null;
        const setup = this.habitStore.idToSetup()[setupId];
        if (habit) {
            const updatedHabit = { ...habit, timerAnswer: [...(habit.timerAnswer || []), habitTimerAnswer] };
            this.habitStore.updateHabits([updatedHabit]);
        } else {
            const updatedHabit = {
                ...this.habitStore.getNewHabit(),
                setupId,
                dueAt: getCustomDate(dateString),
                timerAnswer: [habitTimerAnswer]
            };
            this.habitStore.addHabit(updatedHabit);
        };

        const repeatCount = setup?.durationRepeatCount || 1;
        const alertToneType: AlertToneType = setup?.alarmSoundType || 'none';
        const alertToneVolume: number = setup?.alarmSoundVolume || 100;
        const timerStopType: TimerStopType = setup?.timerStopType || 'onRoundEnd';

        const totalSeconds = setup?.timerGoal ? this.parseMinutes(setup.timerGoal).totalSeconds : 0;
        const currentRound = this.getCurrentRound(state.totalSecondsSpent, totalSeconds, repeatCount);
        if (totalSeconds && ((timerStopType === 'onRoundEnd' && (state.totalSecondsSpent === (totalSeconds * currentRound))) || (timerStopType === 'onGoalReached' && (state.totalSecondsSpent === (totalSeconds * repeatCount))))
        ) {
            this.openTimerEndDialog(currentRound, repeatCount, alertToneType, alertToneVolume);
        }

        delete existingTimers[id];
        this.timers.set(existingTimers);
        this.storageService.setHabitTimers(existingTimers);
        if (Object.keys(existingTimers).length === 0) {
            clearInterval(this.timerTickInterval);
            this.timerTickInterval = null;
            this.timerTick.set(0);
        }
    };

    openTimerEndDialog(round: number, totalRound: number, alertToneType: AlertToneType, alertToneVolume: number) {
        if (alertToneType !== 'none') {
            this.playAlertTone(alertToneType, alertToneVolume);
            this.alertInterval = setInterval(() => {
                this.playAlertTone(alertToneType, alertToneVolume);
            }, 3000);
        }
        this.dialog.open(HabitTimerEndDialogComponent, {
            maxWidth: '390px',
            width: '100%',
            data: { round, totalRound }
        });
    }

    playAlertTone(alertToneType: AlertToneType, alertToneVolume: number) {
        const audio = new Audio();
        audio.src = `assets/tones/${this.cc.toneMap[alertToneType]}`;
        audio.load();
        audio.volume = Math.min(Math.max(Number(alertToneVolume) / 100, 0), 1);
        audio.play();
    }

    stopAlertTone() {
        clearInterval(this.alertInterval);
        this.alertInterval = null;
    }

    getCurrentRound(
        completedSeconds: number,
        roundSeconds: number,
        roundCount: number
    ): number {
        if (roundCount <= 0 || roundSeconds <= 0) return 0;

        // ceil ensures round changes only after a full roundSeconds is completed
        let currentRound = Math.ceil(completedSeconds / roundSeconds);

        // edge case: 0 completed seconds → still round 1
        if (completedSeconds === 0) currentRound = 1;

        // clamp between 1 and roundCount
        return Math.min(Math.max(currentRound, 1), roundCount);
    }

    parseMinutes(isoDuration: string): { year: number, month: number, day: number, hour: number, minute: number, second: number, totalSeconds: number } {
        const regex = /P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?T?(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/;
        const match = isoDuration.match(regex);

        if (!match) return { year: 0, month: 0, day: 0, hour: 0, minute: 0, second: 0, totalSeconds: 0 };

        const [, year, month, day, hour, minute, second] = match.map(v => parseInt(v || '0', 10));
        const totalSeconds = (hour * 60 * 60) + (minute * 60) + second;

        return { year, month, day, hour, minute, second, totalSeconds };
    }

    getTotalSecondsSpent(timerAnswer: HabitTimerAnswer[] | null): number {
        let totalMs = 0;

        timerAnswer?.forEach((answer: HabitTimerAnswer) => {
            const start = new Date(answer.startTimestamp).getTime();
            const end = new Date(answer.endTimestamp).getTime();
            totalMs += Math.max(0, end - start);
        });

        return Math.floor(totalMs / 1000); // in seconds
    }

    destroy() {
        clearInterval(this.timerTickInterval);
        this.timerTickInterval = null;
        this.timerTick.set(0);
        clearInterval(this.alertInterval);
        this.alertInterval = null;
    }

}