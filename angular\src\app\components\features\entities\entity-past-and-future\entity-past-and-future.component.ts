import { CommonModule } from '@angular/common';
import { Component, computed, inject, Signal, signal, WritableSignal } from '@angular/core';
import { FeatureStore } from '@app/_stores';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { EntityNameType, ShowTypeKey } from '@app/_types/generic.type';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { MatMenuModule } from '@angular/material/menu';
import { InputDropdownConfig } from '@app/_interfaces/generic.interface';
import { Router } from '@angular/router';
import { TodoTabComponent } from '../../todos/todo-tab/todo-tab.component';
import { JournalTabComponent } from '../../journals/journal-tab/journal-tab.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { MatTabsModule } from '@angular/material/tabs';
import { CacheService } from '@app/_services/cache.service';
import { HabitTabComponent } from '../../habits/habit-tab/habit-tab.component';
import { CalendarTabComponent } from '../../calendar-integration/calendar-tab/calendar-tab.component';
import { MoneyTrackerTabComponent } from '../../money-tracker/money-tracker-tab/money-tracker-tab.component';
import { getDateString } from '@app/_utils/utils';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-entity-past-and-future',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatMenuModule,
    SvgComponent,
    InputDropdownComponent,
    TodoTabComponent,
    HabitTabComponent,
    JournalTabComponent,
    MoneyTrackerTabComponent,
    CalendarTabComponent
  ],
  templateUrl: './entity-past-and-future.component.html',
  styleUrl: './entity-past-and-future.component.scss'
})

export class EntityPastAndFutureComponent {

  mode = signal<'PAST' | 'FUTURE'>('PAST');
  readonly featureStore = inject(FeatureStore);
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.ss.viewSignals;
  activeTab: WritableSignal<EntityNameType> = signal('todo');
  activeEntities: Signal<EntityNameType[]> =  computed(() => {
    return this.featureStore.activeEntities().filter(entity => entity !== 'list' && entity !== 'note');
  });
  activeEntity: Signal<EntityNameType> = computed(() => this.activeEntities().length === 1 ? this.activeEntities()[0] : this.activeTab());
  config: { [key: string]: { [key: string]: { name: string, icon: string, groupBy: string, groupedViewType: string, showType: ShowTypeKey, show: string, descriptionType: string, hiddenGroupValues: string[], hidddenGroupedViewTypeValues: string[], hiddenShowValues: string[], filter: boolean } } } = {
    PAST: {
      todo: {
        name: this.cc.texts()['tab_pastTodo_title'],
        icon: 'todo',
        groupBy: 'pastTodoGroupBy',
        groupedViewType: 'pastTodoGroupedViewType',
        showType: 'pastTodoShowType',
        show: 'pastTodoShow',
        descriptionType: 'pastTodoDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      habit: {
        name: this.cc.texts()['tab_pastHabit_title'],
        icon: 'habit',
        groupBy: 'pastHabitGroupBy',
        groupedViewType: 'pastHabitGroupedViewType',
        showType: 'pastHabitShowType',
        show: 'pastHabitShow',
        descriptionType: 'pastHabitDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'date', 'checklist', 'setup', 'calendarName'],
        filter: false
      },
      journal: {
        name: this.cc.texts()['tab_pastJournal_title'],
        icon: 'journal',
        groupBy: 'pastJournalGroupBy',
        groupedViewType: 'pastJournalGroupedViewType',
        showType: 'pastJournalShowType',
        show: 'pastJournalShow',
        descriptionType: 'pastJournalDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'label', 'checklist', 'emptyDays', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      moneyTracker: {
        name: this.cc.texts()['tab_pastMoneyTracker_title'],
        icon: 'moneyTracker',
        groupBy: 'pastMoneyTrackerGroupBy',
        groupedViewType: 'pastMoneyTrackerGroupedViewType',
        showType: 'pastMoneyTrackerShowType',
        show: 'pastMoneyTrackerShow',
        descriptionType: 'pastMoneyTrackerDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood'],
        hidddenGroupedViewTypeValues: ['showCounts'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'habitResponse', 'date', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'calendarName'],
        filter: false
      },
      calendarIntegration: {
        name: this.cc.texts()['tab_pastCalendarEvents_title'],
        icon: 'calendarIntegration',
        groupBy: 'pastCalendarEventGroupBy',
        groupedViewType: 'pastCalendarEventGroupedViewType',
        showType: 'pastCalendarEventShowType',
        show: 'pastCalendarEventShow',
        descriptionType: 'pastCalendarEventDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'mood', 'transactionType', 'setup', 'hashtag'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'checklist', 'attachments', 'hashtag', 'emptyDays', 'description'],
        filter: false
      }
    },
    FUTURE: {
      todo: {
        name: this.cc.texts()['tab_futureTodo_title'],
        icon: 'todo',
        groupBy: 'futureTodoGroupBy',
        groupedViewType: 'futureTodoGroupedViewType',
        showType: 'futureTodoShowType',
        show: 'futureTodoShow',
        descriptionType: 'futureTodoDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      habit: {
        name: this.cc.texts()['tab_futureHabit_title'],
        icon: 'habit',
        groupBy: 'futureHabitGroupBy',
        groupedViewType: 'futureHabitGroupedViewType',
        showType: 'futureHabitShowType',
        show: 'futureHabitShow',
        descriptionType: 'futureHabitDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'date', 'checklist', 'setup', 'calendarName'],
        filter: false
      },
      journal: {
        name: this.cc.texts()['tab_futureJournal_title'],
        icon: 'journal',
        groupBy: 'futureJournalGroupBy',
        groupedViewType: 'futureJournalGroupedViewType',
        showType: 'futureJournalShowType',
        show: 'futureJournalShow',
        descriptionType: 'futureJournalDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'label', 'checklist', 'date', 'habitResponse', 'setup', 'calendarName'],
        filter: false
      },
      moneyTracker: {
        name: this.cc.texts()['tab_futureMoneyTracker_title'],
        icon: 'moneyTracker',
        groupBy: 'futureMoneyTrackerGroupBy',
        groupedViewType: 'futureMoneyTrackerGroupedViewType',
        showType: 'futureMoneyTrackerShowType',
        show: 'futureMoneyTrackerShow',
        descriptionType: 'futureMoneyTrackerDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood'],
        hidddenGroupedViewTypeValues: ['showCounts'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'habitResponse', 'date', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'calendarName'],
        filter: false
      },
      calendarIntegration: {
        name: this.cc.texts()['tab_futureCalendarEvents_title'],
        icon: 'calendarIntegration',
        groupBy: 'futureCalendarEventGroupBy',
        groupedViewType: 'futureCalendarEventGroupedViewType',
        showType: 'futureCalendarEventShowType',
        show: 'futureCalendarEventShow',
        descriptionType: 'futureCalendarEventDescriptionType',
        hiddenGroupValues: ['chronological', 'category', 'hashtag', 'mood', 'transactionType', 'setup'],
        hidddenGroupedViewTypeValues: ['showNetAmount'],
        hiddenShowValues: ['completedAt', 'mood', 'label', 'invalidEntry', 'date', 'habitResponse', 'setup', 'checklist', 'attachments', 'hashtag', 'emptyDays', 'description'],
        filter: false
      }
    }
  }
  entityShowConfig: Signal<{ [key: string]: InputDropdownConfig }> = computed(() => {
    const config = this.vs.entityShowConfig();
    config['description'].signalName = this.config[this.mode()][this.activeEntity()]['descriptionType'];
    return config;
  });

  constructor(private router: Router, public vs: ViewSettingService, public cc: CacheService, public ss: SettingsService) {
    const mode = this.router.url.includes('past') ? 'PAST' : 'FUTURE';
    this.mode.set(mode);
  }

  setTab(tabIndex: number) {
    this.activeTab.set(this.activeEntities()[tabIndex]);
  }

  updateEvent() {
    this.ss.updateViewSettings();
  }

  isDataAvailable: Signal<boolean> = computed(() => {
    const store = this.featureStore.storeMap()[this.activeEntity()];
    const firstDateString = store?.firstDateString();
    const lastDateString = store?.lastDateString();
    const todayDateString = getDateString();
    console.log('entity===>', this.activeEntity(), 'firstDateString', firstDateString, 'lastDateString', lastDateString, 'todayDateString', todayDateString);
    const mode = this.mode();
    switch (mode) {
      case 'PAST':
        return firstDateString < todayDateString;
      case 'FUTURE':
        return lastDateString === null ? true : lastDateString > todayDateString;
    }
  });
}

