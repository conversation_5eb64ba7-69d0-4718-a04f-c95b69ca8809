import { Pipe, PipeTransform } from '@angular/core';
import { SettingsService } from '@app/_services/settings.service';

@Pipe({
    name: 'parseTime',
    standalone: true,
})
export class ParseTimePipe implements PipeTransform {

    constructor(private ss: SettingsService) { }

    transform(time?: string): string {
        if (time === '00:00' || !time) return this.ss.texts()['screen_common_noTime'];

        const [hourStr, minuteStr] = time.split(':');
        const hour = parseInt(hourStr, 10);
        const minute = parseInt(minuteStr, 10);

        const period = hour >= 12 ? this.ss.texts()['overlay_timePicker_pm'] : this.ss.texts()['overlay_timePicker_am'];
        const hour12 = hour % 12 === 0 ? 12 : hour % 12;

        return `${hour12}:${minute.toString().padStart(2, '0')} ${period}`;
    }
}