import { Pipe, PipeTransform } from '@angular/core';
import { CacheService } from '@app/_services/cache.service';
import { SettingsService } from '@app/_services/settings.service';

@Pipe({
  name: 'parseUpdateResponse',
  standalone: true,
})

export class ParseUpdatedResponsePipe implements PipeTransform {

  constructor(private ss: SettingsService, private cc: CacheService) { }

  transform(value?: Date | null, nameString?: string): string {
    if (!value) return this.ss.texts()['screen_common_noTime'];

    const date = new Date(value);
    const now = new Date();

    if (isNaN(date.getTime())) return this.ss.texts()['screen_common_noTime'];

    const diffMs = now.getTime() - date.getTime();
    const seconds = Math.floor(diffMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (nameString) {
      const [name, hashedEmail] = nameString.split('⟨⟩');
      if (name === this.cc.user.name && hashedEmail === this.cc.user.hashedEmail) {
        if (seconds <= 60) return this.ss.texts()['screen_common_lastUpdatedByOwnerInSeconds'];
        if (minutes < 60) return this.ss.interpolateText('screen_common_lastUpdatedByOwnerInMinutes', { minuteCount: minutes.toString() });
        if (hours < 24) return this.ss.interpolateText('screen_common_lastUpdatedByOwnerInHours', { hourCount: hours.toString() });
        if (days < 10) return this.ss.interpolateText('screen_common_lastUpdatedByOwnerInDays', { dayCount: days.toString() });
        return this.ss.interpolateText('screen_common_lastUpdatedByOwnerInDate', { date: this.ss.getFormattedDate(date, 'd MMM yyyy') });
      } else {
        if (seconds <= 60) return this.ss.interpolateText('screen_common_lastUpdatedByMemberInSeconds', { name: name });
        if (minutes < 60) return this.ss.interpolateText('screen_common_lastUpdatedByMemberInMinutes', { minuteCount: minutes.toString(), name: name });
        if (hours < 24) return this.ss.interpolateText('screen_common_lastUpdatedByMemberInHours', { hourCount: hours.toString(), name: name });
        if (days < 10) return this.ss.interpolateText('screen_common_lastUpdatedByMemberInDays', { dayCount: days.toString(), name: name });
        return this.ss.interpolateText('screen_common_lastUpdatedByMemberInDate', { date: this.ss.getFormattedDate(date, 'd MMM yyyy'), name: name });
      }
    } else {
      if (seconds <= 60) return this.ss.texts()['screen_common_updatedInSeconds'];
      if (minutes < 60) return this.ss.interpolateText('screen_common_updatedInMinutes', { minutes: minutes.toString() });
      if (hours < 24) return this.ss.interpolateText('screen_common_updatedInHours', { hours: hours.toString() });
      return this.ss.interpolateText('screen_common_updatedInDays', { days: days.toString() });
    }
  }
}