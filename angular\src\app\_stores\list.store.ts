import { List } from "@app/_interfaces/list.interface";
import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from "@ngrx/signals";
import { computed, inject, signal, WritableSignal } from "@angular/core";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { firstValueFrom } from "rxjs";
import { IndexDbService } from "@app/_services/index-db.service";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { environment } from "@environments/environment";
import { CacheService } from "@app/_services/cache.service";
import { SettingsService } from "@app/_services/settings.service";

type ListState = {
    lists: List[];
    activeLists: List[];
    myLists: List[];
    sharedLists: List[];
    isLoading: boolean;
    selectedListIds: string[];
};

const initialState: ListState = {
    lists: [],
    activeLists: [],
    myLists: [],
    sharedLists: [],
    isLoading: false,
    selectedListIds: []
};

export const ListStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed(({ lists, selectedListIds }, ds = inject(DependencyService)) => ({
        selectedLists: computed(() => {
            const ids = selectedListIds();
            return new Map(
                [...ids].map(id => [id, lists().find(list => list.id === id) || null])
            );
        }),

        idToList: computed<Record<string, List>>(() => {
            const listMap: Record<string, List> = {};
            lists().forEach(list => {
                listMap[list.id] = list;
            });
            return listMap;
        }),
    })),

    withMethods((
        store,
        idbService = inject(IndexDbService),
        ss = inject(SettingsService),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        utilsService = inject(UtilsService),
        cc = inject(CacheService)
    ) => ({

        addList: async (listData: List) => {

            const list: List = listData;
            await idbService.add('lists', listData);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...list });
            await firebaseFunctionService.uploadData(syncRequest);
        },

        updateLists: async (lists: List[], isTemporaryUpdate = false) => {
            const oldLists = [];
            const newLists = [];
            for (const list of lists) {
                const oldList = await firstValueFrom(idbService.getEntityById('lists', list.id));
                list!.localUpdatedAt = new Date();
                list!.lastUpdatedAt = new Date();
                const newList = await idbService.update('lists', list.id, list);
                newList!.cloudUpdatedAt = null;
                oldLists.push(oldList);
                newLists.push(newList);
            }

            if (isTemporaryUpdate) {
                return;
            }

            const syncRequest = cryptoService.preparePatchData(
                oldLists,
                newLists,
                FirestoreCollection.Lists
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        deleteList: async (lists: List[]) => {
            const oldLists = [];
            const newLists = [];
            for (const list of lists) {
                list.deletedAt = new Date();
                const oldList = await firstValueFrom(idbService.getEntityById('lists', list.id));
                const newList = await idbService.update('lists', list.id, list);
                newList!.localUpdatedAt = new Date();
                newList!.cloudUpdatedAt = null;
                oldLists.push(oldList);
                newLists.push(newList);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldLists,
                newLists,
                FirestoreCollection.Lists
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getNewList: (): List => {
            const user = cc.user;
            return {
                id: utilsService.getNewId(),
                title: '',
                description: '',
                uid: user?.uid ?? '',
                ownerName: user?.name,
                ownerEmail: user?.email,
                createdAt: new Date(),
                sessionId: utilsService.getNewId(),
                docVer: environment.dbVersion,
                docCollection: FirestoreCollection.Lists.toString(),
                source: 'client',
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'description',
                        'ownerName',
                        'ownerEmail',
                        'listItems{}.item',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                listItems: {},
                position: 3000,
                isPublic: false,
                publicId: null,
                members: { memberHashedEmails: [], membersConfig: {} },
                collaboratorLimit: 5,
                inviteLink: null,
                hashtags: [],
                isFav: false,
                addOnType: 'checkbox',
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                lastUpdatedAt: new Date(),
                permaDeletedAt: null,
                deletedAt: null,
            }
        },

        selectList: (id: string) => {
            patchState(store, (state) => ({ selectedListIds: [...state.selectedListIds, id] }));
        },

        deselectList: (id: string) => {
            patchState(store, (state) => ({ selectedListIds: state.selectedListIds.filter(listId => listId !== id) }));
        },

        computeList: () => {
            const lists = store.lists();
            const myLists: List[] = [];
            const sharedLists: List[] = [];

            const sortedLists = lists.sort((a, b) => {
                // const posA = a.position ?? -Infinity;
                // const posB = b.position ?? -Infinity;

                // if (posA !== posB) return posB - posA;

                const createdA = new Date(a.createdAt || new Date(0)).getTime();
                const createdB = new Date(b.createdAt || new Date(0)).getTime();

                return createdB - createdA;
            });

            sortedLists.forEach(list => {
                if (list.uid === cc.user.uid) {
                    myLists.push(list);
                } else {
                    sharedLists.push(list);
                }
            });

            patchState(store, { myLists, sharedLists });
        },

        filterLists: (lists: List[], searchQuery: string, isFav: boolean, isCompleted: boolean | null, tags: string[]) => {
            const query = searchQuery.toLowerCase();
            return lists.filter(item => {
                const matchesFav = !isFav || item.isFav === true;
                const listItems = item.listItems ? Object.values(item.listItems).filter(item => !item.deletedAt) : [];
                const matchesCompleted = item.listItems ? (isCompleted ? Object.values(listItems).every(item => item?.done === true) && listItems.length > 0 : !Object.values(listItems).every(item => item?.done === true) || listItems.length === 0) : false;
                const matchesTags = tags.some(tag => item.hashtags?.includes(tag));

                const matchesQuery = !query || item.title.toLowerCase().includes(query) || (item.description && item.description.toLowerCase().includes(query)); // Apply search query

                return !item.deletedAt && matchesFav && matchesQuery && (isCompleted !== null ? matchesCompleted : true) && (tags.length > 0 ? matchesTags : true);
            });
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            idbService.lists$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    patchState(store, { lists: data });
                    store.computeList();
                }
            });
        },
    }),
);
