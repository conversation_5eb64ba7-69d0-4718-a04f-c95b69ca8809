import { Language, LanguageCode } from "@app/_types/generic.type";

export const languages: LanguageCode[] = ['en', 'de', 'fr', 'es', 'it', 'pt'];

export const languageMap: { [key in LanguageCode]: { name: string; value: string } } = {
    'en': { name: 'English', value: 'english' },
    'de': { name: 'German', value: 'german' },
    'fr': { name: 'French', value: 'french' },
    'es': { name: 'Spanish', value: 'spanish' },
    'it': { name: 'Italian', value: 'italian' },
    'pt': { name: 'Portuguese', value: 'portuguese' },
};

export const languageToCodeMap: { [key in Language]: LanguageCode } = {
    'english': 'en',
    'german': 'de',
    'french': 'fr',
    'spanish': 'es',
    'italian': 'it',
    'portuguese': 'pt',
}

export const countryToLanguageMap: { [countryCode: string]: LanguageCode } = {
    'in': 'en',
    'us': 'en',
    'gb': 'en',
    'ca': 'en',
    'au': 'en',
    'de': 'de',
    'at': 'de',
    'ch': 'de',
    'fr': 'fr',
    'be': 'fr',
    'es': 'es',
    'mx': 'es',
    'ar': 'es',
    'it': 'it',
    'pt': 'pt',
    'br': 'pt',
};