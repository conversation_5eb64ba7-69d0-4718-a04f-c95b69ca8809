import { signalStore, withComputed, withState, withMethods, withHooks, patchState } from "@ngrx/signals";
import { computed, effect, inject } from "@angular/core";
import { DependencyService } from "@app/_services/dependency.service";
import { UtilsService } from "@app/_services/utils.service";
import { IndexDbService } from "@app/_services/index-db.service";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { Todo } from "@app/_interfaces/todo.interface";
import { FirestoreCollection } from "@app/_enums/firestore-collection.enum";
import { CryptographyService } from "@app/_services/cryptography.service";
import { FirebaseFunctionService } from "@app/_services/firebase-function.service";
import { firstValueFrom } from "rxjs";
import { CalendarEntitySetup, CalendarEventEntity, EntitySetup } from "@app/_interfaces/feature.interface";
import { getDateString, getTodayDateWithoutTime } from "@app/_utils/utils";
import { DayCode } from "@app/_types/generic.type";
import { emptyRuleRecord } from "@app/_datas/const.data";
import { RuleProcessor } from "@app/_processors/rule.processor";
import { environment } from "@environments/environment";
import { CacheService } from "@app/_services/cache.service";
import { SettingsService } from "@app/_services/settings.service";

type TodoState = {
    todos: Todo[];
    isLoading: boolean;
    filter: { date: Date };
    selectedTodoIds: string[];
    ruleMap: {
        oneDay: { [key in string]: EntitySetup[] },
        daily: EntitySetup[],
        weekly: { [key in DayCode]: EntitySetup[] },
        monthly: { [key in string]: { [key in string]: EntitySetup[] } },
        ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } },
    };
};

const initialState: TodoState = {
    todos: [],
    isLoading: false,
    filter: { date: new Date() },
    selectedTodoIds: [],
    ruleMap: emptyRuleRecord,
};

export const TodoStore = signalStore(
    { providedIn: 'root' },
    withState(initialState),

    withComputed((store, ss = inject(SettingsService), ds = inject(DependencyService)) => ({
        activeTodos: computed<Todo[]>(() => {
            return store.todos().filter(todo => !todo.deletedAt);
        }),
        firstDateString: computed<string>(() => {
            let todayDateString = getDateString();
            const setups = store.todos();
            if (!setups || setups.length === 0) {
                return todayDateString;
            }

            return setups.reduce(
                (acc, setup) => {
                    if (setup.deletedAt || !setup.startAt) return acc;
                    const dateStr = setup.startAt.dateString;

                    if (!acc || (acc && dateStr < acc)) {
                        acc = dateStr;
                    }

                    return acc;
                },
                todayDateString
            );
        }),
        lastDateString: computed<string | null>(() => {
            const setups = store.todos();
            const todayDateString = getDateString();

            if (!setups || setups.length === 0) {
                return null;
            }

            let lastDate: string | null = null;

            for (const setup of setups) {
                const date = setup.endAt?.dateString ?? null;

                if (setup.deletedAt || !setup.startAt || (date && date > todayDateString) || setup.repeat.length === 0) continue;

                if (date === null) return null;

                if (!lastDate || date > lastDate) {
                    lastDate = date;
                }
            }

            return lastDate;
        }),
        idToTodo: computed<Record<string, Todo>>(() => {
            const idToTodo: Record<string, Todo> = {};
            store.todos().forEach(setup => {
                idToTodo[setup.id] = setup;
            });
            return idToTodo;
        }),
        selectedTodos: computed(() => {
            const ids = store.selectedTodoIds();
            return new Map(
                [...ids].map(id => [id, store.todos().find(todo => todo.id === id) || null])
            );
        }),
        unscheduledTodos: computed<EntitySetup[]>(() => {
            const entitySetups: EntitySetup[] = [];
            store.todos().forEach(todo => {
                if (todo.deletedAt || todo.startAt) return;
                entitySetups.push({
                    id: todo.id,
                    entityName: 'todo',
                    startAt: todo.startAt,
                    endAt: todo.endAt,
                    title: todo.title,
                    duration: todo.duration,
                    invalid: false,
                    status: 'all',
                    tags: todo.tags || [],
                    repeatType: 'all',
                    percentage: 0,
                    actions: todo.actions || [],
                });
            });
            return entitySetups;
        }),
        // ruleMap: computed<{
        //     oneDay: { [key in string]: EntitySetup[] },
        //     daily: EntitySetup[],
        //     weekly: { [key in DayCode]: EntitySetup[] },
        //     monthly: { [key in string]: { [key in string]: EntitySetup[] } },
        //     ordinally: { [key in string]: { [key in DayCode]: EntitySetup[] } },
        // }>(() => {
        //     const { oneDay, daily, weekly, monthly, ordinally } = ds.computeRule(store.todos(), 'todo');
        //     return { oneDay, daily, weekly, monthly, ordinally };
        // }),
        hashtagMap: computed<{ [key in string]: string }>(() => {
            const hashtags = ss.hashtags();
            const tagMap: { [key in string]: string } = {};
            store.todos().forEach(todo => {
                if (todo.deletedAt) return;
                todo.tags.forEach(tag => {
                    tagMap[tag] = hashtags.find(hashtag => hashtag.id === tag)?.tag ?? '';
                });
            });
            return tagMap;
        }),
        calendarEventEntities: computed<CalendarEventEntity[]>(() => {
            const entities: CalendarEventEntity[] = [];
            store.todos().forEach(entity => {
                entities.push({
                    id: entity.id,
                    data: entity,
                    localUpdatedAt: entity.localUpdatedAt,
                    updateType: 'raw',
                    isCreate: true
                });
            });
            return entities;
        }),
    })),
    withMethods((
        store,
        ss = inject(SettingsService),
        idbService = inject(IndexDbService),
        cryptoService = inject(CryptographyService),
        firebaseFunctionService = inject(FirebaseFunctionService),
        utilsService = inject(UtilsService),
        ds = inject(DependencyService),
        cc = inject(CacheService)
    ) => ({

        addTodo: async (todoData: Todo) => {
            const todo: Todo = todoData;
            await idbService.add('todos', todo);

            // Upload data to the backend
            const syncRequest = cryptoService.prepareRawData({ ...todo });
            await firebaseFunctionService.uploadData(syncRequest);
        },

        updateTodos: async (todos: Todo[]) => {
            const oldTodos = [];
            const newTodos = [];
            for (const todo of todos) {
                const oldTodo = await firstValueFrom(idbService.getEntityById('todos', todo.id));
                todo!.localUpdatedAt = new Date();
                todo.lastUpdatedBy = `${cc.user.name}⟨⟩${cc.user.hashedEmail}`;
                const newTodo = await idbService.update('todos', todo.id, todo);
                newTodo!.cloudUpdatedAt = null;
                oldTodos.push(oldTodo);
                newTodos.push(newTodo);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldTodos,
                newTodos,
                FirestoreCollection.Todos
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        deleteTodos: async (todos: Todo[]) => {
            const oldTodos = [];
            const newTodos = [];
            for (const todo of todos) {
                todo.deletedAt = new Date();
                const oldTodo = await firstValueFrom(idbService.getEntityById('todos', todo.id));
                const newTodo = await idbService.update('todos', todo.id, todo);
                oldTodo!.localUpdatedAt = new Date();
                newTodo!.cloudUpdatedAt = null;
                oldTodos.push(oldTodo);
                newTodos.push(newTodo);
            }

            const syncRequest = cryptoService.preparePatchData(
                oldTodos,
                newTodos,
                FirestoreCollection.Todos
            );

            await firebaseFunctionService.uploadData(syncRequest);
        },

        getComputedEntities: (dateString: string, filterCompleted: boolean = false, groupBy: 'date' | 'hashtag' = 'date'): { withTime: EntitySetup[], withoutTime: EntitySetup[], calenderEvents: CalendarEntitySetup[], groupedData: { [key in string]: EntitySetup[] } } => {
            const [day, weekday, nthWeek, month, isLastDay] = ds.parseDateInfo(dateString);
            let entityList: EntitySetup[] = [];
            const computedTodos = store.ruleMap();
            entityList = [...(computedTodos.oneDay[dateString] || []), ...computedTodos.daily, ...computedTodos.weekly[weekday], ...computedTodos.monthly[month][day], ...computedTodos.ordinally[nthWeek][weekday], ...(isLastDay ? computedTodos.monthly[month]['-1'] : [])];
            const entitiesWithTime: EntitySetup[] = [];
            const entitiesWithoutTime: EntitySetup[] = [];
            const calenderEvents: CalendarEntitySetup[] = [];
            const groupedData: { [key in string]: EntitySetup[] } = {};
            const today = getTodayDateWithoutTime();

            entityList.forEach(entity => {
                const startsBeforeOrOn = entity.startAt ? entity.startAt.dateString <= dateString : true;
                const endsAfterOrOn = entity.endAt ? entity.endAt.dateString >= dateString : true;
                const isInDateRange = startsBeforeOrOn && endsAfterOrOn;

                if (!isInDateRange) return;

                const clonedEntity = { ...entity };

                const todo = store.idToTodo()[clonedEntity.id] || null;
                const isCompleted = todo?.actions.some(action => (action.isSkipped === false || action.isSkipped === true) && action.dueAt === dateString);
                if (filterCompleted && isCompleted) {
                    return;
                }
                const isSkipped = todo?.actions.some(action => action.isSkipped === true && action.dueAt === dateString);
                const isMissed = new Date(`${dateString}T${clonedEntity.startAt?.timeString}:00`) < today && !isCompleted;
                clonedEntity.invalid = isMissed;
                clonedEntity.status = isCompleted && !isSkipped ? 'completed' : isSkipped ? 'skipped' : isMissed ? 'missed' : 'none';


                const hasTime = clonedEntity.startAt && clonedEntity.startAt.timeString !== "00:00";
                if (hasTime) {
                    entitiesWithTime.push(clonedEntity);
                    const startDate = new Date(`${dateString}T${clonedEntity.startAt?.timeString}:00`);
                    calenderEvents.push({
                        id: clonedEntity.id,
                        start: startDate,
                        end: new Date(startDate.getTime() + clonedEntity.duration * 60 * 1000),
                        title: clonedEntity.title,
                        cssClass: clonedEntity.status,
                        meta: {
                            entity: 'todo',
                            startAt: clonedEntity.startAt,
                            endAt: clonedEntity.endAt,
                            invalid: isMissed
                        }
                    })
                } else {
                    entitiesWithoutTime.push(clonedEntity);
                }

                switch (groupBy) {
                    case 'date':
                        (groupedData[dateString] ??= []).push(clonedEntity);
                        break;
                    case 'hashtag':
                        if (clonedEntity.tags.length === 0) {
                            (groupedData[''] ??= []).push(clonedEntity);
                        } else {
                            clonedEntity.tags.forEach(tag => {
                                const id = tag || '';
                                (groupedData[id] ??= []).push(clonedEntity);
                            });
                        }
                        break;
                }
            })
            return { withTime: entitiesWithTime, withoutTime: entitiesWithoutTime, calenderEvents, groupedData: groupedData };
        },

        getNewTodo: (): Todo => {
            const user = cc.user;
            return {
                id: utilsService.getNewId(),
                uid: user?.uid ?? '',
                docVer: environment.dbVersion,
                docCollection: FirestoreCollection.Todos.toString(),
                ownerName: user?.name,
                ownerEmail: user?.email,
                source: 'client',
                encData: {
                    dek: cryptoService.createEncryptedDocKey(),
                    encFields: [
                        'title',
                        'description',
                        'checkLists[].checklistText',
                        'ownerName',
                        'ownerEmail',
                        'lastUpdatedBy',
                        'members.membersConfig{}.eEmail',
                    ]
                },
                title: '',
                description: '',
                startAt: {
                    dateString: '',
                    timeString: '',
                    dateTime: new Date(),
                    timeWithOffset: ''
                },
                members: { memberHashedEmails: [], membersConfig: {} },
                isStartTimeSet: false,
                isTmzAffected: true,
                endAt: null,
                reminderAt: [],
                repeat: [],
                duration: 15,
                tags: [],
                attachments: [],
                actions: [],
                checkLists: [],
                sessionId: utilsService.getNewId(),
                lastUpdatedBy: user?.name ?? '',
                createdAt: new Date(),
                localUpdatedAt: new Date(),
                cloudUpdatedAt: new Date(),
                permaDeletedAt: null,
                deletedAt: null,
            }
        },
        selectTodo: (id: string) => {
            patchState(store, (state) => ({ selectedTodoIds: [...state.selectedTodoIds, id] }));
        }
    })),

    withHooks({
        async onInit(
            store,
            idbService = inject(IndexDbService)
        ) {
            effect(async () => {
                const setups = store.todos();
                const processor = new RuleProcessor();
                const ruleMap = await processor.computeRule(setups, 'todo');
                patchState(store, { ruleMap });
            });
            
            idbService.todos$.pipe(takeUntilDestroyed()).subscribe(data => {
                if (data) {
                    console.log('todos =======================', data);
                    patchState(store, { todos: data });
                }
            });
        },
    }),
);
