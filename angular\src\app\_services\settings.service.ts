import { computed, effect, inject, Injectable, Renderer2, RendererFactory2, signal, Signal, WritableSignal } from '@angular/core';
import { languageToCodeMap } from '@app/_datas/languages.data';
import { Colors, Feedback, Themes } from '@app/_interfaces/generic.interface';
import { AppThemeType, CollaboraterStatus, Language, LanguageCode, ShowType, ShowTypeKey, subscriptionPlan, ThemeMode } from '@app/_types/generic.type';
import { environment } from '@environments/environment';
import * as translatedTexts from '@app/_datas/translations.json';
import * as themeColors from '@app/_datas/colors.json';
import { formatDate } from '@angular/common';
import { IndexDbService } from './index-db.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Hashtag, UserMetaData, UserResource, UserViewSettings } from '@app/_interfaces/user.interface';
import { firstValueFrom } from 'rxjs/internal/firstValueFrom';
import { CryptographyService } from './cryptography.service';
import { FirestoreCollection } from '@app/_enums/firestore-collection.enum';
import { FirebaseFunctionService } from './firebase-function.service';
import { Collection } from '@app/_types/collection.type';
import { getNewId } from '@app/_utils/utils';
import { CacheService } from './cache.service';

@Injectable({
    providedIn: 'root',
})

export class SettingsService {

    env: Signal<string> = signal(environment.env);
    platform: Signal<string> = signal(environment.platform);
    appVersion: Signal<string> = signal(environment.appVersion);
    appSubVersion: Signal<string> = signal(environment.appSubVersion);
    buildVersion: Signal<string> = signal(environment.buildVersion);
    dbVersion: Signal<number> = signal(environment.dbVersion);
    buildUpdatedDate: Signal<Date> = signal(new Date(environment.buildUpdatedAt));

    //
    userMetadata: WritableSignal<UserMetaData> = signal({} as UserMetaData);
    viewSettings: WritableSignal<UserViewSettings> = signal({} as UserViewSettings);
    userResource: WritableSignal<UserResource> = signal({} as UserResource);

    // Language
    languageToCodeMap: Signal<{ [key in Language]: LanguageCode }> = signal(languageToCodeMap);
    appLanguage: WritableSignal<Language> = signal(this.viewSettings()?.appSettings?.language || 'english');
    language: Signal<LanguageCode> = computed(() => {
        const appLanguage = this.appLanguage();
        return this.languageToCodeMap()[appLanguage];
    });
    texts: Signal<Record<string, string>> = computed(() => {
        return translatedTexts.data[this.language()] as unknown as Record<string, string>;
    });

    // Color themes
    themeColor: WritableSignal<string> = signal(this.viewSettings()?.appSettings?.themeColor || 'theme1');
    appTheme: WritableSignal<AppThemeType> = signal(this.viewSettings()?.appSettings?.appTheme || 'dark');
    mode: Signal<ThemeMode> = computed(() => {
        return this.appTheme() === 'systemDefault' ? this.isBrowserDarkMode() ? 'dark' : 'light' : this.appTheme() as ThemeMode;
    });
    themes: Signal<Record<string, Themes>> = computed(() => {
        return themeColors.data.themes;
    });

    theme: Signal<Colors> = computed(() => {
        const themeValue = this.themeColor();
        const appMode = this.mode();
        const selectedTheme = this.capitalizeFirstLetter(themeValue);
        const selectedAppTheme = this.capitalizeFirstLetter(appMode) as 'Dark' | 'Light';
        const selectedThemeData: Record<string, string> = this.themes()[selectedTheme][selectedAppTheme];
        this.setCurrentTheme(selectedThemeData);
        return {
            color1: selectedThemeData['Color 1'],
            color2: selectedThemeData['Color 2'],
            color3: selectedThemeData['Color 3'],
            color4: selectedThemeData['Color 4'],
            color5: selectedThemeData['Color 5'],
            color6: selectedThemeData['Color 6'],
            color7: selectedThemeData['Color 7'],
            color8: selectedThemeData['Color 8'],
            color9: selectedThemeData['Color 9'],
            color10: selectedThemeData['Color 10'],
            color11: selectedThemeData['Color 11'],
            color12: selectedThemeData['Color 12'],
            color13: selectedThemeData['Color 13'],
            color14: selectedThemeData['Color 14'],
            color15: selectedThemeData['Color 15'],
            color16: selectedThemeData['Color 16'],
            color17: selectedThemeData['Color 17'],
            color18: selectedThemeData['Color 18'],
            color19: selectedThemeData['Color 19'],
            color20: selectedThemeData['Color 20'],
            color21: selectedThemeData['Color 21'],
            color22: selectedThemeData['Color 22'],
            color23: selectedThemeData['Color 23'],
            color24: selectedThemeData['Color 24'],
            color25: selectedThemeData['Color 25'],
            color26: selectedThemeData['Color 26'],
            color27: selectedThemeData['Color 27'],
            color28: selectedThemeData['Color 28'],
            color29: selectedThemeData['Color 29'],
            color30: selectedThemeData['Color 30'],
            color31: selectedThemeData['Color 31'],
            color32: selectedThemeData['Color 32'],
            color33: selectedThemeData['Color 33'],
            color34: selectedThemeData['Color 34'],
            color35: selectedThemeData['Color 35'],
            color36: selectedThemeData['Color 36'],
            color37: selectedThemeData['Color 37'],
        }
    });

    // Storage limit

    storageLimit: Signal<number> = computed(() => {
        const plan = this.userMetadata()?.subscriptionInfo.entitlement ?? 'free' as subscriptionPlan;
        switch (plan) {
            case 'free': return 100 * 1024 * 1024;
            case 'basic': return 100 * 1024 * 1024;
            case 'pro': return 100 * 1024 * 1024;
            case 'plus': return 100 * 1024 * 1024;
            default: return 100 * 1024 * 1024;
        }
    });

    hashtags: Signal<Hashtag[]> = computed(() => {
        return this.userResource()?.tags ?? [];
    });

    tagMap: Signal<{ [key: string]: string }> = computed(() => {
        return Object.fromEntries(this.hashtags().map(item => [item.id, item.tag]));
    });

    metaSignals: Signal<Record<string, WritableSignal<any>>> = computed<Record<string, WritableSignal<any>>>(() => {
        return {
            // User info
            name: signal(this.userMetadata()?.userInfo.name ?? ''),
            email: signal(this.userMetadata()?.userInfo.email ?? ''),
            pseudoName: signal(this.userMetadata()?.userInfo.pseudoName ?? null),
            createdAt: signal(this.userMetadata()?.userInfo.createdAt ?? null),
            storageUsed: signal(this.userMetadata()?.userInfo.storageUsed ?? 0),
            tokensValidAfterTime: signal(this.userMetadata()?.userInfo.tokensValidAfterTime ?? null),

            // Security
            isBiometricEnabled: signal(this.userMetadata()?.securitySettings.isBiometricEnabled ?? false),
            isPasscodeEnabled: signal(this.userMetadata()?.securitySettings.isPasscodeEnabled ?? false),
            passcode: signal(this.userMetadata()?.securitySettings.passcode ?? null),
            isUsingCustomKey: signal(this.userMetadata()?.userInfo.isUsingCustomKey ?? false),
            resetPasscode: signal(this.userMetadata()?.userInfo.resetPasscode ?? false),

            // Data
            exportStatus: signal(this.userMetadata()?.dataBackupInfo.export.status ?? 'none'),
            exportLastUpdatedAt: signal(this.userMetadata()?.dataBackupInfo.export.lastUpdatedAt ?? null),
            importStatus: signal(this.userMetadata()?.dataBackupInfo.import.status ?? 'none'),
            importLastUpdatedAt: signal(this.userMetadata()?.dataBackupInfo.import.lastUpdatedAt ?? null),

            // Subscription
            subscriptionPlan: signal(this.userMetadata()?.subscriptionInfo.entitlement ?? 'free'),
            subscriptionState: signal(this.userMetadata()?.subscriptionInfo.subscriptionState ?? 'none'),
            subscriptionExpDate: signal(this.userMetadata()?.subscriptionInfo.subscriptionExpDate ?? null),
            subscriptionStartDate: signal(this.userMetadata()?.subscriptionInfo.subscriptionStartDate ?? null),
            subscriptionType: signal(this.userMetadata()?.subscriptionInfo.subscriptionType ?? null),
            storeType: signal(this.userMetadata()?.subscriptionInfo.storeType ?? null),
            productId: signal(this.userMetadata()?.subscriptionInfo.productId ?? null),
            unsubscribedAt: signal(this.userMetadata()?.subscriptionInfo.unsubscribedAt ?? null),

            // Super subscription
            superSubscription: signal(this.userMetadata()?.superSubscription ?? null),
        };
    });

    viewSignals: Signal<Record<string, WritableSignal<any>>> = computed<Record<string, WritableSignal<any>>>(() => {
        return {

            // App settings
            appTheme: signal(this.viewSettings().appSettings.appTheme),
            themeColor: signal(this.viewSettings().appSettings.themeColor),
            language: signal(this.viewSettings().appSettings.language),
            supportLanguage: signal(this.viewSettings().appSettings.supportLanguage),
            isVibrationEnabled: signal(this.viewSettings().appSettings.isVibrationEnabled),
            isSpeechToTextEnabled: signal(this.viewSettings().appSettings.isSpeechToTextEnabled),

            // Notes
            noteGroupBy: signal(this.viewSettings().noteSettings.myNoteSettings.notesGroupBy),
            noteShowType: signal(this.viewSettings().noteSettings.myNoteSettings.viewType),
            noteShow: signal(this.getShowValues(this.viewSettings().noteSettings.myNoteSettings)),
            noteDescriptionType: signal(this.viewSettings().noteSettings.myNoteSettings.noteDescriptionType),
            noteGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().noteSettings.myNoteSettings)),

            sharedNoteGroupBy: signal(this.viewSettings().noteSettings.sharedNoteSettings.notesGroupBy),
            sharedNoteShowType: signal(this.viewSettings().noteSettings.sharedNoteSettings.viewType),
            sharedNoteShow: signal(this.getShowValues(this.viewSettings().noteSettings.sharedNoteSettings)),
            sharedNoteDescriptionType: signal(this.viewSettings().noteSettings.sharedNoteSettings.noteDescriptionType),
            sharedNoteGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().noteSettings.sharedNoteSettings)),

            savedNoteGroupBy: signal(this.viewSettings().noteSettings.savedNoteSettings.notesGroupBy),
            savedNoteShowType: signal(this.viewSettings().noteSettings.savedNoteSettings.viewType),
            savedNoteShow: signal(this.getShowValues(this.viewSettings().noteSettings.savedNoteSettings)),
            savedNoteDescriptionType: signal(this.viewSettings().noteSettings.savedNoteSettings.noteDescriptionType),
            savedNoteGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().noteSettings.savedNoteSettings)),

            // Lists
            listGroupBy: signal(this.viewSettings().listSettings.myListSettings.groupByType),
            listShowType: signal(this.viewSettings().listSettings.myListSettings.viewSettingType),
            listShow: signal(this.getShowValues(this.viewSettings().listSettings.myListSettings)),
            listGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().listSettings.myListSettings)),
            listItemShowType: signal(this.viewSettings().listSettings.myListItemSheetSettings.viewType),
            listItemShow: signal(this.getShowValues(this.viewSettings().listSettings.myListItemSheetSettings)),

            sharedListGroupBy: signal(this.viewSettings().listSettings.sharedListSettings.groupByType),
            sharedListShowType: signal(this.viewSettings().listSettings.sharedListSettings.viewSettingType),
            sharedListShow: signal(this.getShowValues(this.viewSettings().listSettings.sharedListSettings)),
            sharedListGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().listSettings.sharedListSettings)),
            sharedListItemShowType: signal(this.viewSettings().listSettings.sharedListItemSettings.viewType),
            sharedListItemShow: signal(this.getShowValues(this.viewSettings().listSettings.sharedListItemSettings)),

            // Features

            showTodoFeature: signal(this.viewSettings().featureSettings.showTodoFeature),
            showNoteFeature: signal(this.viewSettings().featureSettings.showNoteFeature),
            showListFeature: signal(this.viewSettings().featureSettings.showListFeature),

            hiddenCalendarsMap: signal(this.viewSettings().featureSettings.hiddenCalendars),
            hiddenCalendars: signal(Object.keys(this.viewSettings().featureSettings.hiddenCalendars)),
            hiddenHabitsMap: signal(this.viewSettings().featureSettings.hiddenHabits),
            hiddenHabits: signal(Object.keys(this.viewSettings().featureSettings.hiddenHabits)),
            hiddenJournalsMap: signal(this.viewSettings().featureSettings.hiddenJournals),
            hiddenJournals: signal(Object.keys(this.viewSettings().featureSettings.hiddenJournals)),
            hiddenMoneytrackersMap: signal(this.viewSettings().featureSettings.hiddenMoneytrackers),
            hiddenMoneytrackers: signal(Object.keys(this.viewSettings().featureSettings.hiddenMoneytrackers)),

            // Entitys
            entityGroupBy: signal(this.viewSettings().featureSettings.viewType),
            entityShowType: signal(this.viewSettings().todaySettings.todayTabSettings.viewSettingType),
            entityShow: signal(this.getShowValues({ ...this.viewSettings().todaySettings.todayTabSettings, showLabel: this.viewSettings().featureSettings.showFeatureLabels })),
            entityDescriptionType: signal(this.viewSettings().todaySettings.todayTabSettings.descriptionType),
            userGoal: signal(this.viewSettings().featureSettings.userGoal),
            showUserGoal: signal(this.viewSettings().featureSettings.showUserGoal),
            showTimebox: signal(this.viewSettings().featureSettings.showTimebox),
            showCalendarView: signal(this.viewSettings().featureSettings.showCalendarView),
            hideCompletedItems: signal(this.viewSettings().featureSettings.hideCompletedItems),

            // Overdue screen

            overdueShowType: signal(this.viewSettings().todaySettings.overdueSettings.viewSettingType),
            overdueShow: signal(this.getShowValues(this.viewSettings().todaySettings.overdueSettings)),
            overdueDescriptionType: signal(this.viewSettings().todaySettings.overdueSettings.descriptionType),

            // Unscheduled screen

            unscheduleShowType: signal(this.viewSettings().todaySettings.unscheduleSettings.viewSettingType),
            unscheduleShow: signal(this.getShowValues(this.viewSettings().todaySettings.unscheduleSettings)),
            unscheduleDescriptionType: signal(this.viewSettings().todaySettings.unscheduleSettings.descriptionType),

            // Past todo screen
            pastTodoGroupBy: signal(this.viewSettings().pastSettings.pastTodoSettings.groupBy),
            pastTodoGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().pastSettings.pastTodoSettings)),
            pastTodoShowType: signal(this.viewSettings().pastSettings.pastTodoSettings.viewSettingType),
            pastTodoShow: signal(this.getShowValues(this.viewSettings().pastSettings.pastTodoSettings)),
            pastTodoDescriptionType: signal(this.viewSettings().pastSettings.pastTodoSettings.descriptionType),

            // Past journal screen
            pastJournalGroupBy: signal(this.viewSettings().pastSettings.pastJournalSettings.groupBy),
            pastJournalGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().pastSettings.pastJournalSettings)),
            pastJournalShowType: signal(this.viewSettings().pastSettings.pastJournalSettings.viewSettingType),
            pastJournalShow: signal(this.getShowValues(this.viewSettings().pastSettings.pastJournalSettings)),
            pastJournalDescriptionType: signal(this.viewSettings().pastSettings.pastJournalSettings.descriptionType),

            // Past habit screen
            pastHabitGroupBy: signal(this.viewSettings().pastSettings.pastHabitSettings.groupBy),
            pastHabitGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().pastSettings.pastHabitSettings)),
            pastHabitShowType: signal(this.viewSettings().pastSettings.pastHabitSettings.viewSettingType),
            pastHabitShow: signal(this.getShowValues(this.viewSettings().pastSettings.pastHabitSettings)),
            pastHabitDescriptionType: signal(this.viewSettings().pastSettings.pastHabitSettings.descriptionType),

            // Past money tracker screen
            pastMoneyTrackerGroupBy: signal(this.viewSettings().pastSettings.pastMoneyTrackerSettings.groupBySettings),
            pastMoneyTrackerGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().pastSettings.pastMoneyTrackerSettings)),
            pastMoneyTrackerShowType: signal(this.viewSettings().pastSettings.pastMoneyTrackerSettings.viewSettingType),
            pastMoneyTrackerShow: signal(this.getShowValues(this.viewSettings().pastSettings.pastMoneyTrackerSettings)),
            pastMoneyTrackerDescriptionType: signal(this.viewSettings().pastSettings.pastMoneyTrackerSettings.descriptionType),

            // Past calendar event screen
            pastCalendarEventGroupBy: signal(this.viewSettings().pastSettings.pastCalendarEventSettings.groupBy),
            pastCalendarEventGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().pastSettings.pastCalendarEventSettings)),
            pastCalendarEventShowType: signal(this.viewSettings().pastSettings.pastCalendarEventSettings.viewSettingType),
            pastCalendarEventShow: signal(this.getShowValues(this.viewSettings().pastSettings.pastCalendarEventSettings)),

            // Future todo screen
            futureTodoGroupBy: signal(this.viewSettings().futureSettings.futureTodoSettings.groupBy),
            futureTodoGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().futureSettings.futureTodoSettings)),
            futureTodoShowType: signal(this.viewSettings().futureSettings.futureTodoSettings.viewSettingType),
            futureTodoShow: signal(this.getShowValues(this.viewSettings().futureSettings.futureTodoSettings)),
            futureTodoDescriptionType: signal(this.viewSettings().futureSettings.futureTodoSettings.descriptionType),

            // Future journal screen
            futureJournalGroupBy: signal(this.viewSettings().futureSettings.futureJournalSettings.groupBy),
            futureJournalGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().futureSettings.futureJournalSettings)),
            futureJournalShowType: signal(this.viewSettings().futureSettings.futureJournalSettings.viewSettingType),
            futureJournalShow: signal(this.getShowValues(this.viewSettings().futureSettings.futureJournalSettings)),
            futureJournalDescriptionType: signal(this.viewSettings().futureSettings.futureJournalSettings.descriptionType),

            // Future habit screen
            futureHabitGroupBy: signal(this.viewSettings().futureSettings.futureHabitSettings.groupBy),
            futureHabitGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().futureSettings.futureHabitSettings)),
            futureHabitShowType: signal(this.viewSettings().futureSettings.futureHabitSettings.viewSettingType),
            futureHabitShow: signal(this.getShowValues(this.viewSettings().futureSettings.futureHabitSettings)),
            futureHabitDescriptionType: signal(this.viewSettings().futureSettings.futureHabitSettings.descriptionType),

            // Future money tracker screen
            futureMoneyTrackerGroupBy: signal(this.viewSettings().futureSettings.futureMoneyTrackerSettings.groupBySettings),
            futureMoneyTrackerGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().futureSettings.futureMoneyTrackerSettings)),
            futureMoneyTrackerShowType: signal(this.viewSettings().futureSettings.futureMoneyTrackerSettings.viewSettingType),
            futureMoneyTrackerShow: signal(this.getShowValues(this.viewSettings().futureSettings.futureMoneyTrackerSettings)),
            futureMoneyTrackerDescriptionType: signal(this.viewSettings().futureSettings.futureMoneyTrackerSettings.descriptionType),

            // Future calendar event screen
            futureCalendarEventGroupBy: signal(this.viewSettings().futureSettings.futureCalendarEventSettings.groupBy),
            futureCalendarEventGroupedViewType: signal(this.getGroupedViewType(this.viewSettings().futureSettings.futureCalendarEventSettings)),
            futureCalendarEventShowType: signal(this.viewSettings().futureSettings.futureCalendarEventSettings.viewSettingType),
            futureCalendarEventShow: signal(this.getShowValues(this.viewSettings().futureSettings.futureCalendarEventSettings)),

        };
    });

    private renderer: Renderer2;

    showMap: { [key in ShowTypeKey]: { showSignalKey: string, compact: ShowType[], custom: ShowType[], descriptionTypeKey?: string | null, descriptionTypeValue?: string | null } } = {
        listShowType: {
            showSignalKey: 'listShow',
            compact: ['itemCount'],
            custom: ['itemCount', 'collaboratorsCount']
        },
        listItemShowType: {
            showSignalKey: 'listItemShow',
            compact: [],
            custom: ['description'],
        },
        sharedListShowType: {
            showSignalKey: 'sharedListShow',
            compact: ['itemCount'],
            custom: ['itemCount', 'access'],
        },
        sharedListItemShowType: {
            showSignalKey: 'sharedListItemShow',
            compact: [],
            custom: ['description'],
        },
        noteShowType: {
            showSignalKey: 'noteShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'attachments', 'collaboratorsCount'],
            descriptionTypeKey: 'noteDescriptionType',
            descriptionTypeValue: 'none',
        },
        sharedNoteShowType: {
            showSignalKey: 'sharedNoteShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'attachments', 'access'],
            descriptionTypeKey: 'sharedNoteDescriptionType',
            descriptionTypeValue: 'none',
        },
        savedNoteShowType: {
            showSignalKey: 'savedNoteShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'attachments'],
            descriptionTypeKey: 'savedNoteDescriptionType',
            descriptionTypeValue: 'none',
        },
        entityShowType: {
            showSignalKey: 'entityShow',
            compact: ['time'],
            custom: ['habitResponse', 'mood', 'time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments', 'calendarName', 'label'],
            descriptionTypeKey: 'entityDescriptionType',
            descriptionTypeValue: 'none',
        },
        overdueShowType: {
            showSignalKey: 'overdueShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments'],
            descriptionTypeKey: 'overdueDescriptionType',
            descriptionTypeValue: 'none',
        },
        unscheduleShowType: {
            showSignalKey: 'unscheduleShow',
            compact: ['completedAt'],
            custom: ['completedAt', 'checklist', 'attachments'],
            descriptionTypeKey: 'unscheduleDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastTodoShowType: {
            showSignalKey: 'pastTodoShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments'],
            descriptionTypeKey: 'pastTodoDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureTodoShowType: {
            showSignalKey: 'futureTodoShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'checklist', 'attachments'],
            descriptionTypeKey: 'futureTodoDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastHabitShowType: {
            showSignalKey: 'pastHabitShow',
            compact: ['time'],
            custom: ['habitResponse', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'pastHabitDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureHabitShowType: {
            showSignalKey: 'futureHabitShow',
            compact: ['time'],
            custom: ['habitResponse', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'futureHabitDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastJournalShowType: {
            showSignalKey: 'pastJournalShow',
            compact: ['date'],
            custom: ['mood', 'date', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'pastJournalDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureJournalShowType: {
            showSignalKey: 'futureJournalShow',
            compact: ['time'],
            custom: ['mood', 'time', 'reminder', 'duration', 'repeat', 'attachments'],
            descriptionTypeKey: 'futureJournalDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastMoneyTrackerShowType: {
            showSignalKey: 'pastMoneyTrackerShow',
            compact: ['setup'],
            custom: ['setup', 'attachments'],
            descriptionTypeKey: 'pastMoneyTrackerDescriptionType',
            descriptionTypeValue: 'none',
        },
        futureMoneyTrackerShowType: {
            showSignalKey: 'futureMoneyTrackerShow',
            compact: ['setup'],
            custom: ['setup', 'attachments'],
            descriptionTypeKey: 'futureMoneyTrackerDescriptionType',
            descriptionTypeValue: 'none',
        },
        pastCalendarEventShowType: {
            showSignalKey: 'pastCalendarEventShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'calendarName']
        },
        futureCalendarEventShowType: {
            showSignalKey: 'futureCalendarEventShow',
            compact: ['time'],
            custom: ['time', 'reminder', 'duration', 'repeat', 'calendarName']
        }
    }

    constructor(
        private rendererFactory: RendererFactory2,
        private idbService: IndexDbService,
        private cryptoService: CryptographyService,
        private fbfs: FirebaseFunctionService,
        private cc: CacheService,
    ) {
        effect(() => {
            const appTheme = this.appTheme();
            const mode = this.mode();
            this.updateTheme(mode);
        });
        this.renderer = rendererFactory.createRenderer(null, null);
    }

    init() {
        this.initBrowserModeObserver();
        this.idbService.userResource$.pipe(takeUntilDestroyed()).subscribe(data => {
            if (data) {
                this.userResource.set(data[0]);
                console.log('userResource =======================', data[0]);
            }
        });

        this.idbService.userMetadata$.pipe(takeUntilDestroyed()).subscribe(data => {
            if (data) {
                this.userMetadata.set(data[0]);
                console.log('userMetadata =======================', data[0]);
            }
        });

        this.idbService.releaseConfig$.pipe(takeUntilDestroyed()).subscribe(data => {
            if (data) {
                console.log('releaseConfig =======================', data[0]);
            }
        });

        this.idbService.viewSettings$.pipe(takeUntilDestroyed()).subscribe(data => {
            if (data) {
                const viewSettings = data[0];
                this.viewSettings.set(viewSettings);
            }
        });
    }

    async updateViewSettings() {
        const viewSignals = this.viewSignals();
        const viewSettings: UserViewSettings = {
            ...this.viewSettings(),
            appSettings: {
                appTheme: viewSignals['appTheme'](),
                themeColor: viewSignals['themeColor'](),
                language: viewSignals['language'](),
                supportLanguage: viewSignals['supportLanguage'](),
                isVibrationEnabled: viewSignals['isVibrationEnabled'](),
                isSpeechToTextEnabled: viewSignals['isSpeechToTextEnabled'](),
            },
            noteSettings: {
                ...this.viewSettings().noteSettings,
                myNoteSettings: {
                    noteDescriptionType: viewSignals['noteDescriptionType'](),
                    noteViewTime: viewSignals['noteShow']().includes('time'),
                    noteViewDate: viewSignals['noteShow']().includes('date'),
                    noteViewImage: viewSignals['noteShow']().includes('attachments'),
                    noteViewMood: viewSignals['noteShow']().includes('mood'),
                    noteViewTags: viewSignals['noteShow']().includes('hashtag'),
                    notesGroupBy: viewSignals['noteGroupBy'](),
                    viewType: viewSignals['noteShowType'](),
                    collapsedView: viewSignals['noteGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['noteGroupedViewType']().includes('showCounts'),
                    showMemberCount: viewSignals['noteShow']().includes('collaboratorsCount'),
                    showFilterRow: false,
                },
                sharedNoteSettings: {
                    noteDescriptionType: viewSignals['sharedNoteDescriptionType'](),
                    noteViewTime: viewSignals['sharedNoteShow']().includes('time'),
                    noteViewDate: viewSignals['sharedNoteShow']().includes('date'),
                    noteViewImage: viewSignals['sharedNoteShow']().includes('attachments'),
                    noteViewMood: viewSignals['sharedNoteShow']().includes('mood'),
                    showAccess: viewSignals['sharedNoteShow']().includes('access'),
                    notesGroupBy: viewSignals['sharedNoteGroupBy'](),
                    viewType: viewSignals['sharedNoteShowType'](),
                    collapsedView: viewSignals['sharedNoteGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['sharedNoteGroupedViewType']().includes('showCounts'),
                    showFilterRow: false,
                },
            },
            listSettings: {
                ...this.viewSettings().listSettings,
                myListSettings: {
                    showDescription: viewSignals['listShow']().includes('description'),
                    itemCount: viewSignals['listShow']().includes('itemCount'),
                    showCollaboratorsCount: viewSignals['listShow']().includes('collaboratorsCount'),
                    showInvitedUsersCount: viewSignals['listShow']().includes('invitedUsersCount'),
                    showAwaitingUserCount: viewSignals['listShow']().includes('awaitingUserCount'),
                    showHashtags: viewSignals['listShow']().includes('hashtag'),
                    groupByType: viewSignals['listGroupBy'](),
                    collapsedView: viewSignals['listGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['listGroupedViewType']().includes('showCounts'),
                    viewSettingType: viewSignals['listShowType'](),
                },
                myListItemSheetSettings: {
                    showDescription: viewSignals['listItemShow']().includes('description'),
                    viewType: viewSignals['listItemShowType'](),
                    showLastUpdatedBy: viewSignals['listItemShow']().includes('lastUpdatedBy'),
                    showLastUpdatedAt: viewSignals['listItemShow']().includes('lastUpdatedAt'),
                },
                sharedListSettings: {
                    showDescription: viewSignals['sharedListShow']().includes('description'),
                    itemCount: viewSignals['sharedListShow']().includes('itemCount'),
                    showAccess: viewSignals['sharedListShow']().includes('access'),
                    showHashtags: viewSignals['sharedListShow']().includes('hashtag'),
                    groupByType: viewSignals['sharedListGroupBy'](),
                    collapsedView: viewSignals['sharedListGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['sharedListGroupedViewType']().includes('showCounts'),
                    viewSettingType: viewSignals['sharedListShowType'](),
                },
                sharedListItemSettings: {
                    showDescription: viewSignals['sharedListItemShow']().includes('description'),
                    viewType: viewSignals['sharedListItemShowType'](),
                    showLastUpdatedBy: viewSignals['sharedListItemShow']().includes('lastUpdatedBy'),
                    showLastUpdatedAt: viewSignals['sharedListItemShow']().includes('lastUpdatedAt'),
                },
            },
            featureSettings: {
                ...this.viewSettings().featureSettings,
                hiddenCalendars: viewSignals['hiddenCalendarsMap'](),
                hiddenHabits: viewSignals['hiddenHabitsMap'](),
                hiddenJournals: viewSignals['hiddenJournalsMap'](),
                hiddenMoneytrackers: viewSignals['hiddenMoneytrackersMap'](),
                viewType: viewSignals['entityGroupBy'](),
                userGoal: viewSignals['userGoal'](),
                showUserGoal: viewSignals['showUserGoal'](),
                showTimebox: viewSignals['showTimebox'](),
                showCalendarView: viewSignals['showCalendarView'](),
                hideCompletedItems: viewSignals['hideCompletedItems'](),
                showFeatureLabels: viewSignals['entityShow']().includes('label'),
                showTodoFeature: viewSignals['showTodoFeature'](),
                showNoteFeature: viewSignals['showNoteFeature'](),
                showListFeature: viewSignals['showListFeature'](),
            },
            todaySettings: {
                todayTabSettings: {
                    ...this.viewSettings().todaySettings.todayTabSettings,
                    descriptionType: viewSignals['entityDescriptionType'](),
                    viewSettingType: viewSignals['entityShowType'](),
                    showMood: viewSignals['entityShow']().includes('mood'),
                    showTime: viewSignals['entityShow']().includes('time'),
                    showReminder: viewSignals['entityShow']().includes('reminder'),
                    showDuration: viewSignals['entityShow']().includes('duration'),
                    showRepeat: viewSignals['entityShow']().includes('repeat'),
                    showChecklist: viewSignals['entityShow']().includes('checklist'),
                    showImage: viewSignals['entityShow']().includes('attachments'),
                    showTags: viewSignals['entityShow']().includes('hashtag'),
                    showHabitResponse: viewSignals['entityShow']().includes('habitResponse')
                },
                overdueSettings: {
                    descriptionType: viewSignals['overdueDescriptionType'](),
                    viewSettingType: viewSignals['overdueShowType'](),
                    showTime: viewSignals['overdueShow']().includes('time'),
                    showReminder: viewSignals['overdueShow']().includes('reminder'),
                    showDuration: viewSignals['overdueShow']().includes('duration'),
                    showRepeat: viewSignals['overdueShow']().includes('repeat'),
                    showChecklist: viewSignals['overdueShow']().includes('checklist'),
                    showImage: viewSignals['overdueShow']().includes('attachments'),
                    showTags: viewSignals['overdueShow']().includes('hashtag'),
                },
                unscheduleSettings: {
                    descriptionType: viewSignals['unscheduleDescriptionType'](),
                    viewSettingType: viewSignals['unscheduleShowType'](),
                    showCompletedAt: viewSignals['unscheduleShow']().includes('completedAt'),
                    showChecklist: viewSignals['unscheduleShow']().includes('checklist'),
                    showImage: viewSignals['unscheduleShow']().includes('attachments'),
                    showTags: viewSignals['unscheduleShow']().includes('hashtag'),
                },
            },
            pastSettings: {
                ...this.viewSettings().pastSettings,
                pastTodoSettings: {
                    ...this.viewSettings().pastSettings.pastTodoSettings,
                    groupBy: viewSignals['pastTodoGroupBy'](),
                    viewSettingType: viewSignals['pastTodoShowType'](),
                    collapsedView: viewSignals['pastTodoGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['pastTodoGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['pastTodoDescriptionType'](),
                    showDate: viewSignals['pastTodoShow']().includes('date'),
                    showTime: viewSignals['pastTodoShow']().includes('time'),
                    showReminder: viewSignals['pastTodoShow']().includes('reminder'),
                    showDuration: viewSignals['pastTodoShow']().includes('duration'),
                    showRepeat: viewSignals['pastTodoShow']().includes('repeat'),
                    showChecklist: viewSignals['pastTodoShow']().includes('checklist'),
                    showImage: viewSignals['pastTodoShow']().includes('attachments'),
                    showTags: viewSignals['pastTodoShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['pastTodoShow']().includes('emptyDays')
                },
                pastJournalSettings: {
                    ...this.viewSettings().pastSettings.pastJournalSettings,
                    groupBy: viewSignals['pastJournalGroupBy'](),
                    viewSettingType: viewSignals['pastJournalShowType'](),
                    collapsedView: viewSignals['pastJournalGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['pastJournalGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['pastJournalDescriptionType'](),
                    showDate: viewSignals['pastJournalShow']().includes('date'),
                    showTime: viewSignals['pastJournalShow']().includes('time'),
                    showReminder: viewSignals['pastJournalShow']().includes('reminder'),
                    showDuration: viewSignals['pastJournalShow']().includes('duration'),
                    showRepeat: viewSignals['pastJournalShow']().includes('repeat'),
                    showImage: viewSignals['pastJournalShow']().includes('attachments'),
                    showMood: viewSignals['pastJournalShow']().includes('mood'),
                    showTags: viewSignals['pastJournalShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['pastJournalShow']().includes('emptyDays'),
                    showInvalidEntries: viewSignals['pastJournalShow']().includes('invalidEntry'),
                },
                pastHabitSettings: {
                    ...this.viewSettings().pastSettings.pastHabitSettings,
                    groupBy: viewSignals['pastHabitGroupBy'](),
                    viewSettingType: viewSignals['pastHabitShowType'](),
                    collapsedView: viewSignals['pastHabitGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['pastHabitGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['pastHabitDescriptionType'](),
                    showHabitResponse: viewSignals['pastHabitShow']().includes('habitResponse'),
                    showDate: viewSignals['pastHabitShow']().includes('date'),
                    showTime: viewSignals['pastHabitShow']().includes('time'),
                    showReminder: viewSignals['pastHabitShow']().includes('reminder'),
                    showDuration: viewSignals['pastHabitShow']().includes('duration'),
                    showRepeat: viewSignals['pastHabitShow']().includes('repeat'),
                    showImage: viewSignals['pastHabitShow']().includes('attachments'),
                    showTags: viewSignals['pastHabitShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['pastHabitShow']().includes('emptyDays'),
                    showInvalidEntries: viewSignals['pastHabitShow']().includes('invalidEntry'),
                },
                pastMoneyTrackerSettings: {
                    ...this.viewSettings().pastSettings.pastMoneyTrackerSettings,
                    collapsedView: viewSignals['pastMoneyTrackerGroupedViewType']().includes('collapsedView'),
                    descriptionType: viewSignals['pastMoneyTrackerDescriptionType'](),
                    groupBySettings: viewSignals['pastMoneyTrackerGroupBy'](),
                    viewSettingType: viewSignals['pastMoneyTrackerShowType'](),
                    showImage: viewSignals['pastMoneyTrackerShow']().includes('attachments'),
                    showHashtag: viewSignals['pastMoneyTrackerShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['pastMoneyTrackerShow']().includes('emptyDays'),
                    showNetAmount: viewSignals['pastMoneyTrackerGroupedViewType']().includes('showNetAmount'),
                    showSetupTitle: viewSignals['pastMoneyTrackerShow']().includes('setup'),
                },
                pastCalendarEventSettings: {
                    ...this.viewSettings().pastSettings.pastCalendarEventSettings,
                    groupBy: viewSignals['pastCalendarEventGroupBy'](),
                    viewSettingType: viewSignals['pastCalendarEventShowType'](),
                    collapsedView: viewSignals['pastCalendarEventGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['pastCalendarEventGroupedViewType']().includes('showCounts'),
                    showTime: viewSignals['pastCalendarEventShow']().includes('time'),
                    showReminder: viewSignals['pastCalendarEventShow']().includes('reminder'),
                    showDuration: viewSignals['pastCalendarEventShow']().includes('duration'),
                    showRepeat: viewSignals['pastCalendarEventShow']().includes('repeat'),
                    showCalendarName: viewSignals['pastCalendarEventShow']().includes('calendarName'),
                },
            },
            futureSettings: {
                ...this.viewSettings().futureSettings,
                futureTodoSettings: {
                    ...this.viewSettings().futureSettings.futureTodoSettings,
                    groupBy: viewSignals['futureTodoGroupBy'](),
                    viewSettingType: viewSignals['futureTodoShowType'](),
                    collapsedView: viewSignals['futureTodoGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['futureTodoGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['futureTodoDescriptionType'](),
                    showTime: viewSignals['futureTodoShow']().includes('time'),
                    showReminder: viewSignals['futureTodoShow']().includes('reminder'),
                    showDuration: viewSignals['futureTodoShow']().includes('duration'),
                    showRepeat: viewSignals['futureTodoShow']().includes('repeat'),
                    showChecklist: viewSignals['futureTodoShow']().includes('checklist'),
                    showImage: viewSignals['futureTodoShow']().includes('attachments'),
                    showTags: viewSignals['futureTodoShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['futureTodoShow']().includes('emptyDays'),
                },
                futureJournalSettings: {
                    ...this.viewSettings().futureSettings.futureJournalSettings,
                    groupBy: viewSignals['futureJournalGroupBy'](),
                    viewSettingType: viewSignals['futureJournalShowType'](),
                    collapsedView: viewSignals['futureJournalGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['futureJournalGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['futureJournalDescriptionType'](),
                    showTime: viewSignals['futureJournalShow']().includes('time'),
                    showReminder: viewSignals['futureJournalShow']().includes('reminder'),
                    showDuration: viewSignals['futureJournalShow']().includes('duration'),
                    showRepeat: viewSignals['futureJournalShow']().includes('repeat'),
                    showImage: viewSignals['futureJournalShow']().includes('attachments'),
                    showTags: viewSignals['futureJournalShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['futureJournalShow']().includes('emptyDays'),
                    showInvalidEntries: viewSignals['futureJournalShow']().includes('invalidEntry'),
                    showMood: viewSignals['futureJournalShow']().includes('mood'),
                },
                futureHabitSettings: {
                    ...this.viewSettings().futureSettings.futureHabitSettings,
                    groupBy: viewSignals['futureHabitGroupBy'](),
                    viewSettingType: viewSignals['futureHabitShowType'](),
                    collapsedView: viewSignals['futureHabitGroupedViewType']().includes('collapsedView'),
                    showCounts: viewSignals['futureHabitGroupedViewType']().includes('showCounts'),
                    descriptionType: viewSignals['futureHabitDescriptionType'](),
                    showHabitResponse: viewSignals['futureHabitShow']().includes('habitResponse'),
                    showDate: viewSignals['futureHabitShow']().includes('date'),
                    showTime: viewSignals['futureHabitShow']().includes('time'),
                    showReminder: viewSignals['futureHabitShow']().includes('reminder'),
                    showDuration: viewSignals['futureHabitShow']().includes('duration'),
                    showRepeat: viewSignals['futureHabitShow']().includes('repeat'),
                    showImage: viewSignals['futureHabitShow']().includes('attachments'),
                    showTags: viewSignals['futureHabitShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['futureHabitShow']().includes('emptyDays'),
                    showInvalidEntries: viewSignals['futureHabitShow']().includes('invalidEntry')
                },
                futureMoneyTrackerSettings: {
                    ...this.viewSettings().futureSettings.futureMoneyTrackerSettings,
                    collapsedView: viewSignals['futureMoneyTrackerGroupedViewType']().includes('collapsedView'),
                    descriptionType: viewSignals['futureMoneyTrackerDescriptionType'](),
                    groupBySettings: viewSignals['futureMoneyTrackerGroupBy'](),
                    viewSettingType: viewSignals['futureMoneyTrackerShowType'](),
                    showImage: viewSignals['futureMoneyTrackerShow']().includes('attachments'),
                    showHashtag: viewSignals['futureMoneyTrackerShow']().includes('hashtag'),
                    showEmptyDays: viewSignals['futureMoneyTrackerShow']().includes('emptyDays'),
                    showNetAmount: viewSignals['futureMoneyTrackerGroupedViewType']().includes('showNetAmount'),
                    showSetupTitle: viewSignals['futureMoneyTrackerShow']().includes('setup'),
                },
                futureCalendarEventSettings: {
                    ...this.viewSettings().futureSettings.futureCalendarEventSettings,
                    collapsedView: viewSignals['futureCalendarEventGroupedViewType']().includes('collapsedView'),
                    groupBy: viewSignals['futureCalendarEventGroupBy'](),
                    showCalendarName: viewSignals['futureCalendarEventShow']().includes('calendarName'),
                    showCounts: viewSignals['futureCalendarEventGroupedViewType']().includes('showCounts'),
                    showTime: viewSignals['futureCalendarEventShow']().includes('time'),
                    showReminder: viewSignals['futureCalendarEventShow']().includes('reminder'),
                    showDuration: viewSignals['futureCalendarEventShow']().includes('duration'),
                    showRepeat: viewSignals['futureCalendarEventShow']().includes('repeat'),
                    viewSettingType: viewSignals['futureCalendarEventShowType'](),
                },
            },
        };

        const oldViewSettings = await firstValueFrom(this.idbService.getEntityById('viewSettings', viewSettings.id));
        const newViewSettings = await this.idbService.update('viewSettings', viewSettings.id, viewSettings);
        newViewSettings!.localUpdatedAt = new Date();
        newViewSettings!.cloudUpdatedAt = null;

        const syncRequest = this.cryptoService.preparePatchData(
            [oldViewSettings],
            [newViewSettings],
            FirestoreCollection.ViewSettings
        );

        const res = await this.fbfs.uploadData(syncRequest);
        console.log(" i am called res-->>", res);
        // if(res.success)
    };
    async updateUserSettings() {
        const metaSignals = this.metaSignals();
        const metaData = this.userMetadata();
        if (!metaData) return;
        const userMetadata: UserMetaData = {
            ...metaData,
            securitySettings: {
                isBiometricEnabled: metaSignals['isBiometricEnabled'](),
                isPasscodeEnabled: metaSignals['isPasscodeEnabled'](),
                passcode: metaSignals['passcode'](),
            },
            userInfo: {
                ...metaData.userInfo,
                name: metaSignals['name'](),
                email: metaSignals['email'](),
                pseudoName: metaSignals['pseudoName'](),
                isUsingCustomKey: metaSignals['isUsingCustomKey']()
            }
        };

        const oldMetaSettings = await firstValueFrom(this.idbService.getEntityById('usersMetadata', userMetadata.id));
        const newMetaSettings = await this.idbService.update('usersMetadata', userMetadata.id, userMetadata);
        newMetaSettings!.localUpdatedAt = new Date();
        newMetaSettings!.cloudUpdatedAt = null;

        const syncRequest = this.cryptoService.preparePatchData(
            [oldMetaSettings],
            [newMetaSettings],
            FirestoreCollection.ViewSettings
        );

        const res = await this.fbfs.uploadData(syncRequest);
        console.log(" i am called res-->>", res);
        // if(res.success)
    };
    async addHashtag(hashtag: Hashtag) {
        const userResource = this.userResource();
        if (!userResource) {
            return;
        }
        const oldUserResource = await firstValueFrom(this.idbService.getEntityById('userResources', userResource.id));

        userResource.tags.push(hashtag);
        userResource.localUpdatedAt = new Date();
        userResource.cloudUpdatedAt = null;
        userResource.lastUpdatedAt = new Date();

        const newUserResource = await this.idbService.update('userResources', userResource.id, userResource);

        const syncRequest = this.cryptoService.preparePatchData(
            [oldUserResource],
            [newUserResource],
            FirestoreCollection.UserResources
        );
        await this.fbfs.uploadData(syncRequest);
    };

    async updateHashtag(hashtag: Hashtag, isDelete: boolean = false) {
        const userResource = this.userResource();

        if (!userResource) {
            return;
        }
        const oldUserResource = await firstValueFrom(this.idbService.getEntityById('userResources', userResource.id));
        if (isDelete) {
            userResource.tags = userResource.tags.filter(tag => tag.id !== hashtag.id);
        } else {
            userResource.tags = userResource.tags.map(tag =>
                tag.id === hashtag.id ? { ...tag, ...hashtag } : tag
            );
        }
        userResource.localUpdatedAt = new Date();
        userResource.cloudUpdatedAt = null;
        userResource.lastUpdatedAt = new Date();

        const newUserResource = await this.idbService.update('userResources', userResource.id, userResource);
        const syncRequest = this.cryptoService.preparePatchData(
            [oldUserResource],
            [newUserResource],
            FirestoreCollection.UserResources
        );
        await this.fbfs.uploadData(syncRequest);
    };

    async leaveCollaboratedEntity(status: CollaboraterStatus, entityId: string, collection: Collection) {
        const payload = {
            taskId: entityId,
            userId: this.cc.user.uid,
            hashedEmail: this.cc.user.hashedEmail,
            leaveType: status,
            collection,
        };
        try {
            const response = await this.fbfs.callableFunction('collaboration-leaveOrBlockTask', payload, true);
            console.log('leaveCollaboratedEntity response', response);
        } catch (error) {
            console.log('error', error);
        } finally {
            setTimeout(() => {
            }, 2000);
        }
    };

    async sendFeedback(feedback: Feedback) {
        const feedbackPayload = {
            ...feedback,
            localUpdatedAt: new Date(),
            cloudUpdatedAt: null,
            createdAt: new Date(),
            docVer: this.dbVersion(),
            docCollection: FirestoreCollection.UsersFeedback,
            source: 'client',
            sessionId: getNewId(),
            encData: {
                dek: this.cryptoService.createEncryptedDocKey(),
                encFields: []
            }
        };
        console.log("i am called feedbackPayload--->>", feedbackPayload);
        const syncRequest = this.cryptoService.prepareRawData({ ...feedbackPayload });
        try {
            await this.fbfs.uploadData(syncRequest);
        } catch (error) {
            console.log('error', error);
        }
    };

    initBrowserModeObserver() {
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)')
            .addEventListener('change', (event) => {
                if (this.appTheme() === 'systemDefault') {
                    this.updateTheme(event.matches ? 'dark' : 'light');
                }
            });
    }

    isBrowserDarkMode(): boolean {
        return window.matchMedia('(prefers-color-scheme: dark)').matches;
    }

    updateTheme(newMode: 'light' | 'dark') {
        if (this.mode() === 'dark') {
            this.removeModeClass('me-light-mode');
            this.addModeClass('me-dark-mode');
        } else {
            this.removeModeClass('me-dark-mode');
            this.addModeClass('me-light-mode');
        }
        document.documentElement.setAttribute('data-theme', newMode);
    }

    addModeClass(className: string): void {
        this.renderer.addClass(document.body, className);
    }

    removeModeClass(className: string): void {
        this.renderer.removeClass(document.body, className);
    }

    getEntityShowValues(viewSettings: UserViewSettings): ShowType[] {
        const todaySettings = viewSettings.todaySettings.todayTabSettings;
        const entityShow: ShowType[] = [];
        if (todaySettings.showMood) {
            entityShow.push('mood');
        }
        if (todaySettings.showTime) {
            entityShow.push('time');
        }
        if (todaySettings.showReminder) {
            entityShow.push('reminder');
        }
        if (todaySettings.showDuration) {
            entityShow.push('duration');
        }
        if (todaySettings.showRepeat) {
            entityShow.push('repeat');
        }
        if (todaySettings.showChecklist) {
            entityShow.push('checklist');
        }
        if (todaySettings.showImage) {
            entityShow.push('attachments');
        }
        if (viewSettings.featureSettings.showFeatureLabels) {
            entityShow.push('label');
        }
        if (todaySettings.showTags) {
            entityShow.push('hashtag');
        }
        if (todaySettings.showInvalidEntries) {
            entityShow.push('invalidEntry');
        }
        return entityShow;
    }

    getShowValues(settings: any): ShowType[] {
        // const pastSettings = viewSettings.pastSettings.pastTodoSettings;
        const entityShow: ShowType[] = [];
        if (settings.showHabitResponse) {
            entityShow.push('habitResponse');
        }
        if (settings.showSetupTitle) {
            entityShow.push('setup');
        }
        if (settings.showMood || settings.noteViewMood) {
            entityShow.push('mood');
        }
        if (settings.showDate || settings.noteViewDate) {
            entityShow.push('date');
        }
        if (settings.showTime || settings.noteViewTime) {
            entityShow.push('time');
        }
        if (settings.showReminder) {
            entityShow.push('reminder');
        }
        if (settings.showDuration) {
            entityShow.push('duration');
        }
        if (settings.showRepeat) {
            entityShow.push('repeat');
        }
        if (settings.showChecklist) {
            entityShow.push('checklist');
        }
        if (settings.showImage || settings.noteViewImage) {
            entityShow.push('attachments');
        }
        if (settings.showLabel) {
            entityShow.push('label');
        }
        if (settings.showDescription) {
            entityShow.push('description');
        }
        if (settings.itemCount) {
            entityShow.push('itemCount');
        }
        if (settings.showMemberCount || settings.showCollaboratorsCount) {
            entityShow.push('collaboratorsCount');
        }
        if (settings.showInvitedUsersCount) {
            entityShow.push('invitedUsersCount');
        }
        if (settings.showAwaitingUserCount) {
            entityShow.push('awaitingUserCount');
        }
        if (settings.showTags || settings.noteViewTags || settings.showHashtags || settings.showHashtag) {
            entityShow.push('hashtag');
        }
        if (settings.showEmptyDays) {
            entityShow.push('emptyDays');
        }
        if (settings.showInvalidEntries) {
            entityShow.push('invalidEntry');
        }
        if (settings.showCalendarName) {
            entityShow.push('calendarName');
        }
        if (settings.showLastUpdatedBy) {
            entityShow.push('lastUpdatedBy');
        }
        if (settings.showLastUpdatedAt) {
            entityShow.push('lastUpdatedAt');
        }
        if (settings.showAccess) {
            entityShow.push('access');
        }
        return entityShow;
    }

    getGroupedViewType(settings: { collapsedView: boolean, showCounts?: boolean, showNetAmount?: boolean }): string[] {
        const entityGroupViewType: string[] = [];
        if (settings.collapsedView) {
            entityGroupViewType.push('collapsedView');
        }
        if (settings.showCounts) {
            entityGroupViewType.push('showCounts');
        }
        if (settings.showNetAmount) {
            entityGroupViewType.push('showNetAmount');
        }
        return entityGroupViewType;
    }

    setCurrentTheme(theme: Record<string, string>) {
        document.documentElement.style.setProperty('--color1', theme['Color 1']);
        document.documentElement.style.setProperty('--color2', theme['Color 2']);
        document.documentElement.style.setProperty('--color3', theme['Color 3']);
        document.documentElement.style.setProperty('--color4', theme['Color 4']);
        document.documentElement.style.setProperty('--color5', theme['Color 5']);
        document.documentElement.style.setProperty('--color6', theme['Color 6']);
        document.documentElement.style.setProperty('--color7', theme['Color 7']);
        document.documentElement.style.setProperty('--color8', theme['Color 8']);
        document.documentElement.style.setProperty('--color9', theme['Color 9']);
        document.documentElement.style.setProperty('--color10', theme['Color 10']);
        document.documentElement.style.setProperty('--color11', theme['Color 11']);
        document.documentElement.style.setProperty('--color12', theme['Color 12']);
        document.documentElement.style.setProperty('--color13', theme['Color 13']);
        document.documentElement.style.setProperty('--color14', theme['Color 14']);
        document.documentElement.style.setProperty('--color15', theme['Color 15']);
        document.documentElement.style.setProperty('--color16', theme['Color 16']);
        document.documentElement.style.setProperty('--color17', theme['Color 17']);
        document.documentElement.style.setProperty('--color18', theme['Color 18']);
        document.documentElement.style.setProperty('--color19', theme['Color 19']);
        document.documentElement.style.setProperty('--color20', theme['Color 20']);
        document.documentElement.style.setProperty('--color21', theme['Color 21']);
        document.documentElement.style.setProperty('--color22', theme['Color 22']);
        document.documentElement.style.setProperty('--color23', theme['Color 23']);
        document.documentElement.style.setProperty('--color24', theme['Color 24']);
        document.documentElement.style.setProperty('--color25', theme['Color 25']);
        document.documentElement.style.setProperty('--color26', theme['Color 26']);
        document.documentElement.style.setProperty('--color27', theme['Color 27']);
        document.documentElement.style.setProperty('--color28', theme['Color 28']);
        document.documentElement.style.setProperty('--color29', theme['Color 29']);
        document.documentElement.style.setProperty('--color30', theme['Color 30']);
        document.documentElement.style.setProperty('--color31', theme['Color 31']);
        document.documentElement.style.setProperty('--color32', theme['Color 32']);
        document.documentElement.style.setProperty('--color33', theme['Color 33']);
        document.documentElement.style.setProperty('--color34', theme['Color 34']);
        document.documentElement.style.setProperty('--color35', theme['Color 35']);
        document.documentElement.style.setProperty('--color36', theme['Color 36']);
        document.documentElement.style.setProperty('--color37', theme['Color 37']);
    }

    capitalizeFirstLetter(str: string): string {
        return str.replace(/\b\w/g, char => char.toUpperCase());
    }

    interpolateText(templateName: string, values: Record<string, string>): string {
        const template = this.texts()[templateName];
        const result = template.replace(/{{(.*?)}}/g, (_, key) => values[key.trim()] ?? '');
        return result;
    }

    textsWithVariables(textVars: { var: string; eg: string; }[]) {
        let text = '';
        textVars.forEach((item, index) => {
            text += item.var;
            if (index !== textVars.length - 1) {
                text += ', ';
            }
        });
        return text;
    }

    getFormattedDate(date: Date | string | null, format: string): string {
        return formatDate(date || new Date(), format, this.language());
    }

    destroy() {

    }
}