import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';
import { SettingsService } from '@app/_services/settings.service';

@Component({
  selector: 'app-snack-bar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './snack-bar.component.html',
  styleUrl: './snack-bar.component.scss'
})

export class SnackBarComponent {

  primaryActionText: string = 'screen_common_show';
  secondaryActionText: string = 'screen_common_dismiss';
  messageText: string;

  constructor(
    @Inject(MAT_SNACK_BAR_DATA) public data: SnackBarModel,
    private snackBarRef: MatSnackBarRef<SnackBarComponent>,
    public ss: SettingsService
  ) { 
    this.messageText = data.message;
    this.primaryActionText = data.primaryActionText;
    this.secondaryActionText = data.secondaryActionText;
  }

  primaryAction() {
    this.data.primaryAction?.();
    this.snackBarRef.dismiss();
  }

  secondaryAction() {
    this.data.secondaryAction?.();
    this.snackBarRef.dismiss();
  }
}

export class SnackBarModel {
  constructor(
    public message: string,
    public primaryActionText: string,
    public secondaryActionText: string,
    public primaryAction: () => void,
    public secondaryAction: () => void,
  ) { }
}