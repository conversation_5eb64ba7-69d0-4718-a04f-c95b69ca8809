import { CommonModule } from '@angular/common';
import { Component, computed, inject, Signal, ViewChild, WritableSignal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { FeatureStore, MoneyTrackerStore, HabitStore, TodoStore  } from '@app/_stores';
import { SvgComponent } from '@app/components/shared/svg/svg.component';
import { TodoFormComponent } from '../../todos/todo-form/todo-form.component';
import { Todo } from '@app/_interfaces/todo.interface';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Subject } from 'rxjs';
import { getDateString } from '@app/_utils/utils';
import { ParseMinutesPipe } from '@app/_pipes/parse-minutes.pipe';
import { CacheService } from '@app/_services/cache.service';
import { ParseTimePipe } from '@app/_pipes/parse-time.pipe';
import { InputDropdownComponent } from '@app/components/shared/inputs/input-dropdown/input-dropdown.component';
import { InputGoalComponent } from '@app/components/shared/inputs/input-goal/input-goal.component';
import { InputToggleComponent } from '@app/components/shared/inputs/input-toggle/input-toggle.component';
import { JournalBlockComponent } from '../../journals/journal-block/journal-block.component';
import { TodoBlockComponent } from '../../todos/todo-block/todo-block.component';
import { EntityCalendarComponent } from '../entity-calendar/entity-calendar.component';
import { RelativeDatePipe } from '@app/_pipes/relative-date.pipe';
import { MoneyTransaction } from '@app/_interfaces/money-tracker.interface';
import { MoneyTransactionFormComponent } from '../../money-tracker/money-transaction-form/money-transaction-form.component';
import { InputCalendarComponent, InputCalendarModel } from '@app/components/shared/inputs/input-calendar/input-calendar.component';
import { MoneyTrackerBlockComponent } from '../../money-tracker/money-tracker-block/money-tracker-block.component';
import { CalendarEventBlockComponent } from '../../calendar-integration/calendar-event-block/calendar-event-block.component';
import { HabitBlockComponent } from '../../habits/habit-block/habit-block.component';
import { ViewSettingService } from '@app/_services/view-setting.service';
import { EntityEmptyComponent } from '../entity-empty/entity-empty.component';
import { ShowType } from '@app/_types/generic.type';
import { HabitTimerDialogComponent } from '../../habits/habit-timer-dialog/habit-timer-dialog.component';
import { OverdueTodosComponent } from '../../todos/overdue-todos/overdue-todos.component';
import { UnscheduledTodosComponent } from '../../todos/unscheduled-todos/unscheduled-todos.component';
import { HabitTimerService } from '@app/_services/habit-timer.service';
import { SettingsService } from '@app/_services/settings.service';


@Component({
  selector: 'app-entity',
  standalone: true,
  imports: [
    CommonModule,
    SvgComponent,
    MatMenuModule,
    ParseMinutesPipe,
    ParseTimePipe,
    InputDropdownComponent,
    InputToggleComponent,
    JournalBlockComponent,
    TodoBlockComponent,
    EntityCalendarComponent,
    RouterLink,
    RelativeDatePipe,
    MoneyTrackerBlockComponent,
    CalendarEventBlockComponent,
    HabitBlockComponent,
    EntityEmptyComponent
  ],
  templateUrl: './entity.component.html',
  styleUrl: './entity.component.scss'
})

export class EntityComponent {

  readonly featureStore = inject(FeatureStore);
  readonly todoStore = inject(TodoStore);
  readonly moneyStore = inject(MoneyTrackerStore);

  dateString: string = getDateString();
  todayDateString: string = getDateString();
  unSubscribe = new Subject<void>();
  viewSignals: Signal<Record<string, WritableSignal<any>>> = this.ss.viewSignals;
  currentDate: Date = new Date();
  todayDate: Date = new Date();
  mode: 'PRESENT' | 'PAST' | 'FUTURE' = 'PRESENT';
  @ViewChild('menuTrigger', { static: false }) childMenu!: MatMenuTrigger;
  hiddenValues: Signal<ShowType[]> = computed(() => {
    const hiddenValues: ShowType[] = ['completedAt', 'date', 'emptyDays', 'setup'];
    const activeEntities = this.featureStore.activeEntities();
    const isGrouped = this.viewSignals()['entityGroupBy']() !== 'chronological';
    if (isGrouped) {
      hiddenValues.push('label');
    }
    if(!activeEntities.includes('habit')) {
      hiddenValues.push('habitResponse');
      hiddenValues.push('invalidEntry');
    }
    if(!activeEntities.includes('todo')) {
      hiddenValues.push('checklist');
    }
    if(!activeEntities.includes('calendarIntegration')) {
      hiddenValues.push('calendarName');
    }
    if(!activeEntities.includes('journal')) {
      hiddenValues.push('mood');
      hiddenValues.push('invalidEntry');
    }
    return hiddenValues;
  });
  notApplicableValues: Signal<{value: string, reason: string }[]> = computed(() => {
    const notApplicableValues = [];
    const isGrouped = this.viewSignals()['entityGroupBy']() !== 'chronological';
    if (isGrouped) {
      notApplicableValues.push({ value: 'label', reason: this.cc.texts()['bottomSheet_viewConfiguration_notApplicableContent'] });
    }
    return notApplicableValues;
  });
  isToday: Signal<boolean> = computed(() => {
    return this.dateString === this.todayDateString;
  });

  constructor(private dialog: MatDialog, private router: Router, private route: ActivatedRoute, public cc: CacheService, public vs: ViewSettingService, public timerService: HabitTimerService, public ss: SettingsService) {

  }

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      const date = params.get('date');
      if (date) {
        this.init(date);
      } else {
        const dateString = getDateString();
        this.init(dateString);
      };
    });
  }

  init(date: string) {
    this.dateString = date;
    this.currentDate = new Date(date);
    this.featureStore.filterDate(date);

    if (this.currentDate > this.todayDate) {
      this.mode = 'FUTURE';
    } else if (this.currentDate < this.todayDate) {
      this.mode = 'PAST';
    } else {
      this.mode = 'PRESENT';
    }
  }

  navigateWithOffset(offsetDays: number) {
    let currentDate = this.dateString ? new Date(this.dateString) : new Date();

    // Adjust date
    currentDate.setDate(currentDate.getDate() + offsetDays);

    const formatted = currentDate.toISOString().split('T')[0];

    this.router.navigate(formatted === this.todayDateString ? ['/today'] : [`/`, formatted]);
  }

  openGoal() {
    const confirmDialog = this.dialog.open(InputGoalComponent, {
      width: '100%',
      maxWidth: '500px',
      maxHeight: '90vh',
      disableClose: true,
      data: {
        value: this.viewSignals()['userGoal']() || ''
      },
    });
    return confirmDialog.afterClosed().subscribe(dialogResult => {
      if (dialogResult) {
        this.viewSignals()['userGoal'].set(dialogResult);
        this.ss.updateViewSettings();
      }
    });;
  }

  openOverdueTodos() {
    const dialogRef = this.dialog.open(OverdueTodosComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
    });
  }

  openUnscheduledTodos() {
    const dialogRef = this.dialog.open(UnscheduledTodosComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
    });
  }

  openInputCalendar() {
    const dialogRef = this.dialog.open(InputCalendarComponent, {
      maxWidth: '350px',
      width: '100%',
      minWidth: "300px",
      data: new InputCalendarModel(this.currentDate)
    });

    dialogRef.afterClosed().subscribe((dialogResult: Date | null) => {
      if (dialogResult) {
        const dateString = getDateString(dialogResult);
        this.router.navigate(dateString === this.todayDateString ? ['/today'] : [`/`, dateString]);
      }
    });
  }

  openTodo(todo?: Todo) {
    const confirmDialog = this.dialog.open(TodoFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
      disableClose: true,
      data: {
        mode: todo ? 'edit' : 'new',
        value: todo ? todo : null,
        dateString: this.dateString
      },
    });
    return confirmDialog.afterClosed();
  }

  openMoneyTracker(transactionType: 'income' | 'expense', moneyTransaction?: MoneyTransaction) {
    const confirmDialog = this.dialog.open(MoneyTransactionFormComponent, {
      width: '100%',
      maxWidth: '750px',
      maxHeight: '90vh',
      height: '100%',
      disableClose: true,
      data: {
        mode: moneyTransaction ? 'edit' : 'new',
        value: moneyTransaction ? moneyTransaction : null,
        transactionType: transactionType
      },
    });
    return confirmDialog.afterClosed();
  }

  openAddEntities(menu: MatMenuTrigger, event: MouseEvent) {
    event.stopPropagation();
    event.stopImmediatePropagation();
    menu.closeMenu();
    if(this.moneyStore.allowedSetups().length > 0) {
      menu.openMenu();
    } else {
      this.openTodo();
    }
  }

  openHabitTimers() {
    const dialogRef = this.dialog.open(HabitTimerDialogComponent, {
      maxWidth: '350px',
      width: '100%',
    });

    dialogRef.afterClosed().subscribe((dialogResult: Date | null) => {
      if (dialogResult) {
        console.log(dialogResult);
      }
    });
  }

  ngOnDestroy() {
    this.unSubscribe.complete();
    this.unSubscribe.next();
  }
}
